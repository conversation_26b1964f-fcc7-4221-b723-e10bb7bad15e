import { IconHelp, IconLogout, IconSettings } from '@tabler/icons-react';
import { Stack, Text } from '@mantine/core';
import { useSidebar } from '../SidebarContext';
import { SettingsItem } from './SettingsItem/SettingsItem';
import classes from './SettingsSection.module.css';

const settingsItems = [
  {
    icon: IconSettings,
    label: 'Account Settings',
    href: '/settings',
  },
  {
    icon: IconHelp,
    label: 'Help & Support',
    href: '/help',
  },
  {
    icon: IconLogout,
    label: 'Logout',
    href: '/logout',
  },
];

export function SettingsSection() {
  const { isCollapsed } = useSidebar();

  return (
    <Stack gap="md">
      {!isCollapsed && (
        <Text size="sm" fw={600} c="dimmed" className={classes.sectionTitle}>
          SETTINGS
        </Text>
      )}
      <Stack gap="xs">
        {settingsItems.map((item) => (
          <SettingsItem
            key={item.href}
            icon={item.icon}
            label={item.label}
            href={item.href}
            collapsed={isCollapsed}
          />
        ))}
      </Stack>
    </Stack>
  );
}
