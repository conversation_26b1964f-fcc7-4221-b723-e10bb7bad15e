"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimiters = exports.rateLimitService = exports.RateLimitService = void 0;
const rate_limiter_flexible_1 = require("rate-limiter-flexible");
const logger_1 = require("./logger");
class RateLimitService {
    constructor() {
        this.limiters = new Map();
    }
    /**
     * Create a rate limiter middleware
     */
    createRateLimit(name, config) {
        const limiter = new rate_limiter_flexible_1.RateLimiterMemory({
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration || config.duration
        });
        this.limiters.set(name, limiter);
        return (req, res, next) => __awaiter(this, void 0, void 0, function* () {
            try {
                const key = config.keyGenerator ? config.keyGenerator(req) : this.getDefaultKey(req);
                yield limiter.consume(key);
                next();
            }
            catch (rejRes) {
                const remainingTime = Math.round(rejRes.msBeforeNext / 1000);
                logger_1.logger.warn('Rate limit exceeded', {
                    limiter: name,
                    key: config.keyGenerator ? config.keyGenerator(req) : this.getDefaultKey(req),
                    remainingTime,
                    requestId: req.requestId,
                    ip: req.ip || req.connection.remoteAddress
                });
                res.status(429).json({
                    error: 'Too many requests',
                    message: `Rate limit exceeded. Try again in ${remainingTime} seconds.`,
                    retryAfter: remainingTime
                });
            }
        });
    }
    /**
     * Default key generator (by IP)
     */
    getDefaultKey(req) {
        return req.ip || req.connection.remoteAddress || 'unknown';
    }
}
exports.RateLimitService = RateLimitService;
// Create singleton instance
exports.rateLimitService = new RateLimitService();
// Pre-configured rate limiters
exports.rateLimiters = {
    // General API rate limit: 100 requests per 15 minutes
    general: exports.rateLimitService.createRateLimit('general', {
        points: 100,
        duration: 15 * 60 // 15 minutes
    })
};
