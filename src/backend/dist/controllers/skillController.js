"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillController = void 0;
/**
 * @swagger
 * tags:
 *   name: Skills
 *   description: Skill management endpoints
 */
class SkillController {
    constructor(serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
        /**
         * @swagger
         * /api/skills:
         *   get:
         *     summary: Get all skills
         *     tags: [Skills]
         *     responses:
         *       200:
         *         description: List of all skills
         *         content:
         *           application/json:
         *             schema:
         *               type: array
         *               items:
         *                 $ref: '#/components/schemas/SkillWithCategory'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getSkills = (_, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const skillService = this.serviceRegistry.getSkillService();
                const skills = yield skillService.getSkills();
                res.json(skills);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to fetch skills' });
            }
        });
        /**
         * @swagger
         * /api/skills/{id}:
         *   get:
         *     summary: Get skill by ID
         *     tags: [Skills]
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Skill ID
         *     responses:
         *       200:
         *         description: Skill details
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/SkillWithCategory'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.getSkillById = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const skillService = this.serviceRegistry.getSkillService();
                const skill = yield skillService.getSkillById(Number(id));
                if (!skill) {
                    res.status(404).json({ error: 'Skill not found' });
                    return;
                }
                res.json(skill);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to fetch skill' });
            }
        });
        /**
         * @swagger
         * /api/skills:
         *   post:
         *     summary: Create a new skill
         *     tags: [Skills]
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             $ref: '#/components/schemas/CreateSkillDto'
         *     responses:
         *       201:
         *         description: Skill created successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Skill'
         *       400:
         *         $ref: '#/components/responses/BadRequest'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.createSkill = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { name, categoryId } = req.body;
                if (!name || !categoryId) {
                    res.status(400).json({ error: 'Name and categoryId are required' });
                    return;
                }
                const skillService = this.serviceRegistry.getSkillService();
                const skill = yield skillService.createSkill({ name, categoryId });
                res.status(201).json(skill);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to create skill' });
            }
        });
        /**
         * @swagger
         * /api/skills/{id}:
         *   put:
         *     summary: Update a skill
         *     tags: [Skills]
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Skill ID
         *     requestBody:
         *       required: true
         *       content:
         *         application/json:
         *           schema:
         *             type: object
         *             properties:
         *               name:
         *                 type: string
         *               description:
         *                 type: string
         *               categoryId:
         *                 type: integer
         *     responses:
         *       200:
         *         description: Skill updated successfully
         *         content:
         *           application/json:
         *             schema:
         *               $ref: '#/components/schemas/Skill'
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.updateSkill = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const data = req.body;
                const skillService = this.serviceRegistry.getSkillService();
                const skill = yield skillService.updateSkill(Number(id), data);
                res.json(skill);
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to update skill' });
            }
        });
        /**
         * @swagger
         * /api/skills/{id}:
         *   delete:
         *     summary: Delete a skill
         *     tags: [Skills]
         *     parameters:
         *       - in: path
         *         name: id
         *         required: true
         *         schema:
         *           type: integer
         *         description: Skill ID
         *     responses:
         *       204:
         *         description: Skill deleted successfully
         *       404:
         *         $ref: '#/components/responses/NotFound'
         *       500:
         *         $ref: '#/components/responses/InternalServerError'
         */
        this.deleteSkill = (req, res) => __awaiter(this, void 0, void 0, function* () {
            try {
                const { id } = req.params;
                const skillService = this.serviceRegistry.getSkillService();
                yield skillService.deleteSkill(Number(id));
                res.status(204).send();
            }
            catch (error) {
                res.status(500).json({ error: 'Failed to delete skill' });
            }
        });
    }
}
exports.SkillController = SkillController;
