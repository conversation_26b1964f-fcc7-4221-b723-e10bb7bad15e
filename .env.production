# Production environment configuration
# This file should be created on your production server with secure values

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=CHANGE_ME_SECURE_PRODUCTION_PASSWORD
POSTGRES_DB=agent_service

# AI Provider API Keys
OPENAI_API_KEY=sk-production-openai-key
ANTHROPIC_API_KEY=production-anthropic-key
GROQ_API_KEY=production-groq-key

# LangSmith
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=production-langsmith-key
LANGCHAIN_PROJECT=agent-service-toolkit-production

# Application Settings
LOG_LEVEL=INFO
DEBUG=false

# Security
SECRET_KEY=CHANGE_ME_SECURE_PRODUCTION_SECRET_KEY

# Deployment
VERSION=latest
