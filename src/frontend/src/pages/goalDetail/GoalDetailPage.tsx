import { IconArrowLeft, IconInfoCircle } from '@tabler/icons-react';
import { useNavigate, useParams } from 'react-router-dom';
import { Alert, Button, Container, Group, Stack, Text, Title } from '@mantine/core';
import classes from './GoalDetailPage.module.css';

export function GoalDetailPage() {
  const { goalId } = useParams<{ goalId: string }>();
  const navigate = useNavigate();

  const handleBackToGoals = () => {
    navigate('/goals');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Group justify="space-between" align="flex-start">
          <div>
            <Title order={1} className={classes.title}>
              Goal Details
            </Title>
            <Text size="lg" c="dimmed">
              Goal ID: {goalId || 'Unknown'}
            </Text>
          </div>

          <Group gap="sm">
            <Button
              variant="light"
              leftSection={<IconArrowLeft size={16} />}
              onClick={handleBackToHome}
            >
              Back to Home
            </Button>
            <Button variant="outline" onClick={handleBackToGoals}>
              View All Goals
            </Button>
          </Group>
        </Group>

        <Alert
          icon={<IconInfoCircle size={16} />}
          title="Page Under Development"
          color="blue"
          className={classes.developmentAlert}
        >
          <Text size="sm">
            This is a temporary goal detail page. The full implementation with goal content,
            progress tracking, and interactive features will be added in future iterations.
          </Text>
        </Alert>

        <div className={classes.content}>
          <Stack gap="md">
            <div>
              <Title order={3} mb="sm">
                What's Coming Next
              </Title>
              <Text>This page will eventually display:</Text>
              <ul className={classes.featureList}>
                <li>Detailed goal information and description</li>
                <li>Learning path breakdown and milestones</li>
                <li>Progress tracking and completion status</li>
                <li>Recommended resources and courses</li>
                <li>Goal editing and management options</li>
                <li>Achievement badges and rewards</li>
              </ul>
            </div>

            <div>
              <Title order={3} mb="sm">
                Navigation Flow Established
              </Title>
              <Text>
                ✅ Users can now be automatically redirected here after creating a goal
                <br />
                ✅ Proper URL structure with goal ID parameter is in place
                <br />✅ Navigation back to goals list and home page is functional
              </Text>
            </div>
          </Stack>
        </div>
      </Stack>
    </Container>
  );
}
