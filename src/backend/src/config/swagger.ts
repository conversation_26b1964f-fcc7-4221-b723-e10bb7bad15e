import swaggerJsdoc from 'swagger-jsdoc';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'CodePlus Platform API',
      version: '1.0.0',
      description: 'A Node.js/Express API for managing users, skills, and categories with TypeScript and Prisma ORM',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:4000',
        description: 'Development server'
      }
    ],
    components: {
      schemas: {
        User: {
          type: 'object',
          required: ['name', 'email'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the user'
            },
            name: {
              type: 'string',
              description: 'The name of the user'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the user was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the user was last updated'
            }
          }
        },
        UserWithSkills: {
          allOf: [
            { $ref: '#/components/schemas/User' },
            {
              type: 'object',
              properties: {
                skills: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      skill: { $ref: '#/components/schemas/Skill' }
                    }
                  }
                }
              }
            }
          ]
        },
        CreateUserDto: {
          type: 'object',
          required: ['name', 'email'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the user'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            }
          }
        },
        Category: {
          type: 'object',
          required: ['name'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the category'
            },
            name: {
              type: 'string',
              description: 'The name of the category'
            },
            description: {
              type: 'string',
              description: 'The description of the category'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the category was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the category was last updated'
            }
          }
        },
        CreateCategoryDto: {
          type: 'object',
          required: ['name'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the category'
            },
            description: {
              type: 'string',
              description: 'The description of the category'
            }
          }
        },
        Skill: {
          type: 'object',
          required: ['name', 'categoryId'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the skill'
            },
            name: {
              type: 'string',
              description: 'The name of the skill'
            },
            description: {
              type: 'string',
              description: 'The description of the skill'
            },
            categoryId: {
              type: 'integer',
              description: 'The ID of the category this skill belongs to'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the skill was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the skill was last updated'
            }
          }
        },
        SkillWithCategory: {
          allOf: [
            { $ref: '#/components/schemas/Skill' },
            {
              type: 'object',
              properties: {
                category: { $ref: '#/components/schemas/Category' }
              }
            }
          ]
        },
        CreateSkillDto: {
          type: 'object',
          required: ['name', 'categoryId'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the skill'
            },
            description: {
              type: 'string',
              description: 'The description of the skill'
            },
            categoryId: {
              type: 'integer',
              description: 'The ID of the category this skill belongs to'
            }
          }
        },
        AddSkillToUserDto: {
          type: 'object',
          required: ['skillId'],
          properties: {
            skillId: {
              type: 'integer',
              description: 'The ID of the skill to add to the user'
            }
          }
        },        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error message'
            }
          }        }
      },
      responses: {
        BadRequest: {
          description: 'Bad request',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }        },
        NotFound: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        Conflict: {
          description: 'Conflict - Resource already exists',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        InternalServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    }
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};

export const specs = swaggerJsdoc(options);
