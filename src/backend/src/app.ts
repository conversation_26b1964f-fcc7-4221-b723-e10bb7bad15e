import express from 'express';
import cors from 'cors';
import swaggerUi from 'swagger-ui-express';
import prisma from './prisma/client';
import { ServiceRegistry } from './services';
import { createUserRouter, createCategoryRouter, createSkillRouter } from './routes';
import { specs } from './config/swagger';

const app = express();

console.log('🔧 Initializing Express app...');

// Initialize dependency injection
console.log('🔧 Creating service registry...');
const serviceRegistry = ServiceRegistry.getInstance(prisma);
console.log('✅ Service registry created successfully');

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

// API Info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'CodePlus Platform Backend API',
    version: '1.0.0',
    description: 'Simple REST API for managing users, skills, and categories',
    documentation: '/api-docs',
    endpoints: {
      users: '/api/users',
      categories: '/api/categories',
      skills: '/api/skills'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Routes
console.log('🔧 Setting up API routes...');
app.use('/api/users', createUserRouter(serviceRegistry));
app.use('/api/categories', createCategoryRouter(serviceRegistry));
app.use('/api/skills', createSkillRouter(serviceRegistry));

// 404 handler - catch all unmatched routes
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);
  res.status(500).json({ error: 'Internal server error' });
});

console.log('✅ Express app initialized successfully');

export default app;