"""
LangChain-based semantic retriever for PostgreSQL PGVector database.

This module provides a ResumeRetriever class that implements LangChain's BaseRetriever
interface for querying resume data from the people_skill_set table using semantic search.
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

import openai
import psycopg2
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever

from core.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
EMBEDDING_MODEL = "text-embedding-3-small"
VECTOR_DIM = 1536


class ResumeRetriever(BaseRetriever):
    """
    LangChain-based semantic retriever for PostgreSQL PGVector database.

    This retriever performs semantic search against the people_skill_set table using
    OpenAI embeddings and PGVector cosine similarity search. It supports flexible
    filtering by chunk_type, user_id, chunk_subtype, and other metadata fields.
    """

    model_config = {"arbitrary_types_allowed": True, "extra": "allow"}

    def __init__(
        self,
        db_url: Optional[str] = None,
        k: int = 5,
        chunk_type_filter: Optional[str] = None,
        user_id_filter: Optional[str] = None,
        chunk_subtype_filter: Optional[str] = None,
        metadata_filters: Optional[Dict[str, Any]] = None,
        similarity_threshold: float = 0.0,
        openai_api_key: Optional[str] = None,
        **kwargs: Any
    ):
        """
        Initialize the ResumeRetriever.

        Args:
            db_url: PostgreSQL database connection URL (uses settings if not provided)
            k: Number of top results to return
            chunk_type_filter: Filter by chunk_type (e.g., "skill_group", "work_experience")
            user_id_filter: Filter by specific user_id
            chunk_subtype_filter: Filter by chunk_subtype (e.g., "5-10 years")
            metadata_filters: Dictionary of metadata field filters
            similarity_threshold: Minimum similarity score threshold
            openai_api_key: OpenAI API key (uses environment variable if not provided)
            **kwargs: Additional arguments passed to BaseRetriever
        """
        super().__init__(**kwargs)

        # Set attributes normally since we allow extra fields
        self.db_url = db_url or settings.get_postgresql_url()
        self.k = k
        self.chunk_type_filter = chunk_type_filter
        self.user_id_filter = user_id_filter
        self.chunk_subtype_filter = chunk_subtype_filter
        self.metadata_filters = metadata_filters or {}
        self.similarity_threshold = similarity_threshold
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")

        if not self.openai_api_key:
            raise ValueError("OpenAI API key must be provided either as parameter or environment variable")
    
    def _get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding vector for the given text using OpenAI API.
        
        Args:
            text: Input text to embed
            
        Returns:
            List of float values representing the embedding vector
            
        Raises:
            Exception: If embedding generation fails
        """
        try:
            client = openai.OpenAI(api_key=self.openai_api_key)
            response = client.embeddings.create(
                input=text,
                model=EMBEDDING_MODEL
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding: {str(e)}")
            raise
    
    def _build_query(self, query_embedding: List[float]) -> tuple[str, tuple]:
        """
        Build the SQL query with filters and parameters.
        
        Args:
            query_embedding: The embedding vector for the query
            
        Returns:
            Tuple of (SQL query string, parameters tuple)
        """
        # Base query with cosine similarity search
        base_query = """
            SELECT 
                id,
                user_id,
                source_id,
                full_name,
                email,
                chunk_type,
                chunk_subtype,
                content,
                metadata,
                created_at,
                1 - (embedding <=> %s::vector) AS similarity_score
            FROM people_skill_set
        """
        
        # Build WHERE conditions
        where_conditions = []
        params = [query_embedding]
        
        # Add similarity threshold filter
        if self.similarity_threshold > 0:
            where_conditions.append("1 - (embedding <=> %s::vector) >= %s")
            params.append(self.similarity_threshold)
        
        # Add chunk_type filter
        if self.chunk_type_filter:
            where_conditions.append("chunk_type = %s")
            params.append(self.chunk_type_filter)
        
        # Add user_id filter
        if self.user_id_filter:
            where_conditions.append("user_id = %s")
            params.append(self.user_id_filter)
        
        # Add chunk_subtype filter
        if self.chunk_subtype_filter:
            where_conditions.append("chunk_subtype = %s")
            params.append(self.chunk_subtype_filter)
        
        # Add metadata filters
        for key, value in self.metadata_filters.items():
            where_conditions.append("metadata ->> %s = %s")
            params.extend([key, str(value)])
        
        # Construct final query
        if where_conditions:
            query = base_query + " WHERE " + " AND ".join(where_conditions)
        else:
            query = base_query
        
        # Add ordering and limit
        query += " ORDER BY similarity_score DESC LIMIT %s"
        params.append(self.k)
        
        return query, tuple(params)
    
    def _get_relevant_documents(
        self, 
        query: str, 
        *, 
        run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """
        Retrieve relevant documents for the given query.
        
        Args:
            query: The search query string
            run_manager: Callback manager for the retriever run
            
        Returns:
            List of Document objects with content and metadata
            
        Raises:
            Exception: If database query fails
        """
        try:
            logger.info(f"Retrieving documents for query: '{query[:100]}...'")

            # Generate embedding for the query
            logger.info(f"Generating embedding for query...")
            query_embedding = self._get_embedding(query)
            logger.info(f"Embedding generated successfully, length: {len(query_embedding)}")
            
            # Build SQL query with filters
            sql_query, params = self._build_query(query_embedding)
            
            # Execute database query with timeout
            logger.info(f"Connecting to database...")
            with psycopg2.connect(self.db_url, connect_timeout=10) as conn:
                logger.info(f"Database connected, executing query...")
                with conn.cursor() as cursor:
                    # Set statement timeout to prevent hanging
                    cursor.execute("SET statement_timeout = '30s'")
                    cursor.execute(sql_query, params)
                    results = cursor.fetchall()
                    logger.info(f"Query executed, got {len(results)} results")
            
            # Convert results to Document objects
            documents = []
            for row in results:
                (
                    doc_id, user_id, source_id, full_name, email,
                    chunk_type, chunk_subtype, content, metadata_json,
                    created_at, similarity_score
                ) = row
                
                # Parse metadata JSON - handle both string and dict cases
                try:
                    if metadata_json is None:
                        metadata = {}
                    elif isinstance(metadata_json, dict):
                        # Already parsed by psycopg2 (common with newer versions)
                        metadata = metadata_json.copy()  # Make a copy to avoid mutations
                    elif isinstance(metadata_json, str):
                        # JSON string that needs parsing
                        if metadata_json.strip():
                            metadata = json.loads(metadata_json)
                        else:
                            metadata = {}
                    else:
                        # Handle other types (list, etc.) by converting to dict
                        logger.warning(f"Unexpected metadata type: {type(metadata_json)}, value: {metadata_json}")
                        metadata = {}
                except (json.JSONDecodeError, TypeError, AttributeError) as e:
                    logger.warning(f"Failed to parse metadata: {e}, using empty dict")
                    metadata = {}
                
                # Add database fields to metadata
                metadata.update({
                    "id": doc_id,
                    "user_id": user_id,
                    "source_id": source_id,
                    "full_name": full_name,
                    "email": email,
                    "chunk_type": chunk_type,
                    "chunk_subtype": chunk_subtype,
                    "created_at": created_at.isoformat() if created_at else None,
                    "similarity_score": float(similarity_score)
                })
                
                # Create Document object
                doc = Document(
                    page_content=content,
                    metadata=metadata
                )
                documents.append(doc)
            
            logger.info(f"Retrieved {len(documents)} documents")
            return documents
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}")
            raise
    
    def update_filters(
        self,
        chunk_type_filter: Optional[str] = None,
        user_id_filter: Optional[str] = None,
        chunk_subtype_filter: Optional[str] = None,
        metadata_filters: Optional[Dict[str, Any]] = None,
        k: Optional[int] = None,
        similarity_threshold: Optional[float] = None
    ) -> "ResumeRetriever":
        """
        Create a new ResumeRetriever instance with updated filters.

        Args:
            chunk_type_filter: New chunk_type filter
            user_id_filter: New user_id filter
            chunk_subtype_filter: New chunk_subtype filter
            metadata_filters: New metadata filters
            k: New number of results to return
            similarity_threshold: New similarity threshold

        Returns:
            New ResumeRetriever instance with updated filters
        """
        return ResumeRetriever(
            db_url=self.db_url,
            k=k if k is not None else self.k,
            chunk_type_filter=chunk_type_filter if chunk_type_filter is not None else self.chunk_type_filter,
            user_id_filter=user_id_filter if user_id_filter is not None else self.user_id_filter,
            chunk_subtype_filter=chunk_subtype_filter if chunk_subtype_filter is not None else self.chunk_subtype_filter,
            metadata_filters=metadata_filters if metadata_filters is not None else self.metadata_filters,
            similarity_threshold=similarity_threshold if similarity_threshold is not None else self.similarity_threshold,
            openai_api_key=self.openai_api_key
        )


# Convenience functions for common use cases

def create_skill_retriever(
    experience_level: Optional[str] = None,
    k: int = 5,
    similarity_threshold: float = 0.0
) -> ResumeRetriever:
    """
    Create a retriever specifically for skill-related queries.

    Args:
        experience_level: Filter by experience level (e.g., "5-10 years", "10+ years")
        k: Number of results to return
        similarity_threshold: Minimum similarity score threshold

    Returns:
        ResumeRetriever configured for skill queries
    """
    return ResumeRetriever(
        chunk_type_filter="skill_group",
        chunk_subtype_filter=experience_level,
        k=k,
        similarity_threshold=similarity_threshold
    )


def create_work_experience_retriever(
    company_name: Optional[str] = None,
    k: int = 5,
    similarity_threshold: float = 0.0
) -> ResumeRetriever:
    """
    Create a retriever specifically for work experience queries.

    Args:
        company_name: Filter by specific company name
        k: Number of results to return
        similarity_threshold: Minimum similarity score threshold

    Returns:
        ResumeRetriever configured for work experience queries
    """
    return ResumeRetriever(
        chunk_type_filter="work_experience",
        chunk_subtype_filter=company_name,
        k=k,
        similarity_threshold=similarity_threshold
    )


def create_project_retriever(
    project_name: Optional[str] = None,
    k: int = 5,
    similarity_threshold: float = 0.0
) -> ResumeRetriever:
    """
    Create a retriever specifically for project queries.

    Args:
        project_name: Filter by specific project name
        k: Number of results to return
        similarity_threshold: Minimum similarity score threshold

    Returns:
        ResumeRetriever configured for project queries
    """
    return ResumeRetriever(
        chunk_type_filter="project",
        chunk_subtype_filter=project_name,
        k=k,
        similarity_threshold=similarity_threshold
    )


def create_user_retriever(
    user_id: str,
    chunk_type: Optional[str] = None,
    k: int = 10,
    similarity_threshold: float = 0.0
) -> ResumeRetriever:
    """
    Create a retriever for a specific user's data.

    Args:
        user_id: The specific user ID to query
        chunk_type: Optional filter by chunk type
        k: Number of results to return
        similarity_threshold: Minimum similarity score threshold

    Returns:
        ResumeRetriever configured for specific user queries
    """
    return ResumeRetriever(
        user_id_filter=user_id,
        chunk_type_filter=chunk_type,
        k=k,
        similarity_threshold=similarity_threshold
    )

