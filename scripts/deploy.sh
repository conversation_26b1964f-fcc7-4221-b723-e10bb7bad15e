#!/bin/bash

# Deployment script for Agent Service Toolkit
# Usage: ./scripts/deploy.sh [environment] [version]

set -e

ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
fi

log "Starting deployment to $ENVIRONMENT environment with version $VERSION"

# Check if required tools are installed
command -v docker >/dev/null 2>&1 || error "Docker is required but not installed"
command -v docker-compose >/dev/null 2>&1 || command -v docker compose >/dev/null 2>&1 || error "Docker Compose is required but not installed"

# Set environment-specific variables
case $ENVIRONMENT in
    staging)
        COMPOSE_FILE="compose.yaml"
        ENV_FILE=".env.staging"
        ;;
    production)
        COMPOSE_FILE="compose.yaml -f compose.prod.yml"
        ENV_FILE=".env.production"
        ;;
esac

# Check if environment file exists
if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
    warn "Environment file $ENV_FILE not found. Using default .env file"
    ENV_FILE=".env"
fi

# Export version for docker-compose
export VERSION=$VERSION

log "Using Docker Compose files: $COMPOSE_FILE"
log "Using environment file: $ENV_FILE"

# Change to project root
cd "$PROJECT_ROOT"

# Pull latest images
log "Pulling Docker images..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" pull

# Stop existing services
log "Stopping existing services..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" down

# Start services
log "Starting services..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" up -d

# Wait for services to be healthy
log "Waiting for services to be healthy..."
timeout 300 bash -c '
    while true; do
        if docker compose -f '"$COMPOSE_FILE"' --env-file "'"$ENV_FILE"'" ps | grep -q "unhealthy\|starting"; then
            echo "Waiting for services to be healthy..."
            sleep 5
        else
            break
        fi
    done
'

# Verify deployment
log "Verifying deployment..."

# Check if agent service is responding
if curl -f http://localhost:8080/info >/dev/null 2>&1; then
    log "✓ Agent service is responding"
else
    error "✗ Agent service is not responding"
fi

# Check if streamlit app is responding
if curl -f http://localhost:8501/healthz >/dev/null 2>&1; then
    log "✓ Streamlit app is responding"
else
    error "✗ Streamlit app is not responding"
fi

# Show running services
log "Deployment completed successfully!"
log "Running services:"
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" ps

log "Deployment to $ENVIRONMENT completed successfully with version $VERSION"
