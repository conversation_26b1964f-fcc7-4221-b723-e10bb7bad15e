import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AppLayout } from './components';
import { CoursesPage } from './pages/courses';
import { DashboardPage } from './pages/dashboard';
import { GoalDetailPage } from './pages/goalDetail';
import { GoalsPage } from './pages/goals';
import { HomePage } from './pages/home';
import { LearningPathsPage } from './pages/learningPaths';
import { ProgressPage } from './pages/progress';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AppLayout>
        <HomePage />
      </AppLayout>
    ),
  },
  {
    path: '/dashboard',
    element: (
      <AppLayout>
        <DashboardPage />
      </AppLayout>
    ),
  },
  {
    path: '/goals',
    element: (
      <AppLayout>
        <GoalsPage />
      </AppLayout>
    ),
  },
  {
    path: '/goals/:goalId',
    element: (
      <AppLayout>
        <GoalDetailPage />
      </AppLayout>
    ),
  },
  {
    path: '/learning-paths',
    element: (
      <AppLayout>
        <LearningPathsPage />
      </AppLayout>
    ),
  },
  {
    path: '/courses',
    element: (
      <AppLayout>
        <CoursesPage />
      </AppLayout>
    ),
  },
  {
    path: '/progress',
    element: (
      <AppLayout>
        <ProgressPage />
      </AppLayout>
    ),
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
