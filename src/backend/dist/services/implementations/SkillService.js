"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillService = void 0;
class SkillService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    getSkills() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.skill.findMany({
                include: { category: true }
            });
        });
    }
    getSkillById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.skill.findUnique({
                where: { id },
                include: { category: true }
            });
        });
    }
    createSkill(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.skill.create({
                data
            });
        });
    }
    updateSkill(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.skill.update({
                where: { id },
                data
            });
        });
    }
    deleteSkill(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.prisma.skill.delete({
                where: { id }
            });
        });
    }
}
exports.SkillService = SkillService;
