const { PrismaClient } = require('@prisma/client');

async function waitForDatabase() {
  const prisma = new PrismaClient();
  let retries = 30;
  
  while (retries) {
    try {
      await prisma.$connect();
      console.log('Database connected successfully');
      await prisma.$disconnect();
      return;
    } catch (error) {
      console.log(`Database not ready, retrying... (${retries} retries left)`);
      retries--;
      if (retries === 0) {
        console.error('Failed to connect to database after 30 retries');
        process.exit(1);
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

waitForDatabase();
