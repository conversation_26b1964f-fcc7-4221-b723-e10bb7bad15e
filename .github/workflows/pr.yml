name: PathForge AI - Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  PYTHON_VERSION: "3.12"
  UV_VERSION: "0.5.11"

jobs:
  # Check what files have changed
  changes:
    uses: ./.github/workflows/reusable-changes.yml
    permissions:
      contents: read
      pull-requests: read

  pr-validation:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.python == 'true'
    permissions:
      contents: read
      pull-requests: write
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Validate PR title
      uses: amannn/action-semantic-pull-request@v5
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        types: |
          feat
          fix
          docs
          style
          refactor
          perf
          test
          build
          ci
          chore
          revert

    - name: Check for breaking changes
      run: |
        # Check if there are any breaking changes in the API
        if git diff --name-only origin/${{ github.base_ref }}...HEAD | grep -E "(src/schema/|src/service/service.py)"; then
          echo "::warning::Potential breaking changes detected in API schema or service"
        fi

    - name: Lint commit messages
      uses: wagoid/commitlint-github-action@v6
      with:
        configFile: .commitlintrc

  # wait-for-copilot:
  #   runs-on: ubuntu-latest
  #   needs: pr-validation
  #   if: github.event.action == 'opened' || github.event.action == 'synchronize'
#
  #   steps:
  #   - name: Wait for GitHub Copilot response
  #     uses: actions/github-script@v7
  #     with:
  #       github-token: ${{ secrets.GITHUB_TOKEN }}
  #       script: |
  #         const { owner, repo } = context.repo;
  #         const pull_number = context.payload.pull_request.number;
#
  #         console.log(`Waiting for GitHub Copilot response on PR #${pull_number}`);
#
  #         // Wait up to 10 minutes for Copilot comment
  #         const maxWaitTime = 10 * 60 * 1000; // 10 minutes in milliseconds
  #         const checkInterval = 30 * 1000; // 30 seconds
  #         const startTime = Date.now();
#
  #         while (Date.now() - startTime < maxWaitTime) {
  #           try {
  #             // Get all comments on the PR
  #             const comments = await github.rest.issues.listComments({
  #               owner,
  #               repo,
  #               issue_number: pull_number,
  #             });
#
  #             // Check if there's a comment from GitHub Copilot
  #             const copilotComment = comments.data.find(comment =>
  #               comment.user.login === 'github-copilot[bot]' ||
  #               comment.user.type === 'Bot' && comment.user.login.includes('copilot')
  #             );
#
  #             if (copilotComment) {
  #               console.log('✅ GitHub Copilot has responded to the PR');
  #               console.log(`Copilot comment: ${copilotComment.body.substring(0, 200)}...`);
  #               return;
  #             }
#
  #             console.log('⏳ Still waiting for GitHub Copilot response...');
  #             await new Promise(resolve => setTimeout(resolve, checkInterval));
#
  #           } catch (error) {
  #             console.error('Error checking for Copilot comments:', error);
  #             await new Promise(resolve => setTimeout(resolve, checkInterval));
  #           }
  #         }
#
  #         // If we reach here, timeout occurred
  #         console.log('⚠️ Timeout: No GitHub Copilot response received within 10 minutes');
  #         console.log('Proceeding without Copilot review - manual review required');
#
  #         // Add a comment to the PR indicating timeout
  #         await github.rest.issues.createComment({
  #           owner,
  #           repo,
  #           issue_number: pull_number,
  #           body: '⚠️ **GitHub Copilot Review Timeout**\n\nNo response from GitHub Copilot was received within 10 minutes. Please ensure manual code review is performed before merging.'
  #         });

  code-quality:
    needs: changes
    if: needs.changes.outputs.python == 'true'
    uses: ./.github/workflows/reusable-code-quality.yml
    permissions:
      contents: read
    with:
      python-version: "3.12"
      uv-version: "0.5.11"

  dependency-check:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.python == 'true'
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ env.UV_VERSION }}

    - name: Check for dependency vulnerabilities
      run: |
        uv sync --frozen
        # Install safety for vulnerability checking
        uv add safety
        uv run safety check --json --output safety-report.json || true

    - name: Upload safety report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: safety-report
        path: safety-report.json



  #performance-test:
  #  runs-on: ubuntu-latest
#
  #  steps:
  #  - uses: actions/checkout@v4
#
  #  - name: Set up Python
  #    uses: actions/setup-python@v5
  #    with:
  #      python-version: "3.12"
#
  #  - name: Install uv
  #    uses: astral-sh/setup-uv@v6
#
  #  - name: Install dependencies
  #    run: |
  #      uv sync --frozen --group dev
#
  #  - name: Create test environment file
  #    run: |
  #      cat > .env << EOF
  #      OPENAI_API_KEY=sk-fake-openai-key
  #      POSTGRES_USER=postgres
  #      POSTGRES_PASSWORD=postgres
  #      POSTGRES_DB=agent_service
  #      EOF
#
  #  - name: Start services
  #    run: |
  #      docker compose up -d --wait
#
  #  - name: Run performance tests
  #    run: |
  #      # Basic performance test - check response times
  #      timeout 60 bash -c 'until curl -f http://localhost:8080/info; do sleep 2; done'
#
  #      # Test API response time
  #      response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8080/info)
  #      echo "API response time: ${response_time}s"
#
  #      # Fail if response time is too slow (> 5 seconds)
  #      if (( $(echo "$response_time > 5.0" | bc -l) )); then
  #        echo "::error::API response time too slow: ${response_time}s"
  #        exit 1
  #      fi
#
  #  - name: Cleanup
  #    if: always()
  #    run: |
  #      docker compose down -v

  frontend-test:
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    uses: ./.github/workflows/reusable-frontend-test.yml
    permissions:
      contents: read
    with:
      node-version: "18"

  # Copied from CI pipeline
  test:
    needs: changes
    if: needs.changes.outputs.python == 'true'
    uses: ./.github/workflows/reusable-test.yml
    permissions:
      contents: read
    with:
      python-version: "3.12"
      uv-version: "0.5.11"
      upload-coverage: true
    secrets:
      OPENWEATHERMAP_API_KEY: ${{ secrets.OPENWEATHERMAP_API_KEY }}
      OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
      OPENROUTER_MODEL: ${{ secrets.OPENROUTER_MODEL }}
      OPENROUTER_BASEURL: ${{ secrets.OPENROUTER_BASEURL }}
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  docker-build:
    needs: changes
    if: needs.changes.outputs.agent_service == 'true' || needs.changes.outputs.streamlit_app == 'true' || needs.changes.outputs.frontend_docker == 'true'
    uses: ./.github/workflows/reusable-conditional-docker-build.yml
    permissions:
      contents: read
    with:
      push: false
      report-sizes: true

  # docker-test:
  #   needs: [changes, docker-build]
  #   if: needs.changes.outputs.docker == 'true'
  #   uses: ./.github/workflows/reusable-docker-test.yml
  #   permissions:
  #     contents: read