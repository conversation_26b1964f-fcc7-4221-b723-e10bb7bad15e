# Example environment file
# Copy this to .env and fill in your actual values

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=agent_service

# AI Provider API Keys (choose one or more)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GROQ_API_KEY=your-groq-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# <PERSON><PERSON><PERSON> (optional, for tracing and monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your-langsmith-api-key-here
LANGCHAIN_PROJECT=agent-service-toolkit

# Application Settings
LOG_LEVEL=INFO
DEBUG=false

# Security (generate secure random strings for production)
SECRET_KEY=your-secret-key-here

# External Services (optional)
WEATHER_API_KEY=your-weather-api-key-here

# Deployment specific
VERSION=latest
