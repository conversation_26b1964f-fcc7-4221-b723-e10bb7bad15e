#!/usr/bin/env python3
"""
Test script to verify the JSON metadata parsing fix in ResumeRetriever.

This script demonstrates that the JSON parsing error has been resolved.
"""

import sys
import os
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Set up logging to see detailed information
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def test_retriever_json_fix():
    """Test the retriever with the JSON metadata parsing fix."""
    
    print("🔧 Testing ResumeRetriever JSON Metadata Fix")
    print("=" * 50)
    
    try:
        from agents.resume_retriever import ResumeRetriever
        
        print("✅ Import successful")
        
        # Create retriever with small result set for testing
        retriever = ResumeRetriever(k=2)
        print("✅ Retriever created successfully")
        
        # Test query that previously caused JSON parsing error
        test_query = "Python programming skills"
        print(f"\n🔍 Testing query: '{test_query}'")
        
        docs = retriever.invoke(test_query)
        
        print(f"✅ Query successful! Retrieved {len(docs)} documents")
        
        if docs:
            print("\n📋 Document details:")
            for i, doc in enumerate(docs, 1):
                metadata = doc.metadata
                print(f"\n  Document {i}:")
                print(f"    User: {metadata.get('full_name', 'Unknown')}")
                print(f"    Type: {metadata.get('chunk_type', 'Unknown')}")
                print(f"    Score: {metadata.get('similarity_score', 0):.3f}")
                print(f"    Content: {doc.page_content[:80]}...")
                
                # Test that metadata is properly parsed
                if isinstance(metadata, dict):
                    print(f"    ✅ Metadata is properly parsed as dict")
                else:
                    print(f"    ❌ Metadata type issue: {type(metadata)}")
        else:
            print("ℹ️ No documents found (database might be empty or no matches)")
        
        print(f"\n🎉 JSON metadata parsing fix verified successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        
        # Check if it's the original JSON error
        if "JSON object must be str, bytes or bytearray, not dict" in str(e):
            print("🚨 This is the original JSON parsing error - fix not working")
        else:
            print("ℹ️ Different error - might be database connection or other issue")
        
        import traceback
        traceback.print_exc()
        return False

def test_rag_agent_fix():
    """Test the RAG agent to ensure it works with the fix."""
    
    print("\n🤖 Testing ResumeRAGAgent with Fix")
    print("=" * 50)
    
    try:
        from agents.resume_rag_agent import ResumeRAGAgent
        
        print("✅ RAG Agent import successful")
        
        # Create agent
        agent = ResumeRAGAgent()
        print("✅ RAG Agent created successfully")
        
        # Test the simple query that was failing
        test_query = "Who has Python programming skills?"
        print(f"\n🔍 Testing query: '{test_query}'")
        
        response = agent.query_simple(test_query)
        
        print(f"✅ RAG query successful!")
        print(f"📝 Response: {response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG Agent test failed: {str(e)}")
        
        # Check if it's the original JSON error
        if "JSON object must be str, bytes or bytearray, not dict" in str(e):
            print("🚨 Original JSON parsing error still present in RAG agent")
        else:
            print("ℹ️ Different error in RAG agent")
        
        return False

def main():
    """Run all tests to verify the fix."""
    
    print("🚀 Resume Retriever JSON Fix Verification")
    print("=" * 60)
    print("This script verifies that the JSON metadata parsing error has been fixed.")
    print()
    
    # Test 1: Basic retriever
    retriever_success = test_retriever_json_fix()
    
    # Test 2: RAG agent (if basic retriever works)
    if retriever_success:
        rag_success = test_rag_agent_fix()
        
        if rag_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ JSON metadata parsing fix is working correctly")
            print("✅ ResumeRetriever is functioning properly")
            print("✅ ResumeRAGAgent is working with the fix")
        else:
            print("\n⚠️ PARTIAL SUCCESS")
            print("✅ ResumeRetriever fix is working")
            print("❌ ResumeRAGAgent still has issues")
    else:
        print("\n❌ TESTS FAILED")
        print("❌ ResumeRetriever fix is not working")
        print("Please check the error messages above for debugging information")

if __name__ == "__main__":
    main()
