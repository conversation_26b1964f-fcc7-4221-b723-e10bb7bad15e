import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { logger } from './logger';

interface RateLimitConfig {
  points: number; // Maximum number of requests
  duration: number; // Time window in seconds
  blockDuration?: number; // Block duration in seconds (default: same as duration)
  keyGenerator?: (req: Request) => string;
}

export class RateLimitService {
  private limiters: Map<string, RateLimiterMemory> = new Map();

  /**
   * Create a rate limiter middleware
   */
  createRateLimit(name: string, config: RateLimitConfig) {
    const limiter = new RateLimiterMemory({
      points: config.points,
      duration: config.duration,
      blockDuration: config.blockDuration || config.duration
    });

    this.limiters.set(name, limiter);

    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const key = config.keyGenerator ? config.keyGenerator(req) : this.getDefaultKey(req);
        await limiter.consume(key);
        next();
      } catch (rejRes: any) {
        const remainingTime = Math.round(rejRes.msBeforeNext / 1000);
        
        logger.warn('Rate limit exceeded', {
          limiter: name,
          key: config.keyGenerator ? config.keyGenerator(req) : this.getDefaultKey(req),
          remainingTime,
          requestId: (req as any).requestId,
          ip: req.ip || req.connection.remoteAddress
        });

        res.status(429).json({
          error: 'Too many requests',
          message: `Rate limit exceeded. Try again in ${remainingTime} seconds.`,
          retryAfter: remainingTime
        });
      }
    };
  }
  /**
   * Default key generator (by IP)
   */
  private getDefaultKey(req: Request): string {
    return req.ip || req.connection.remoteAddress || 'unknown';
  }
}

// Create singleton instance
export const rateLimitService = new RateLimitService();

// Pre-configured rate limiters
export const rateLimiters = {
  // General API rate limit: 100 requests per 15 minutes
  general: rateLimitService.createRateLimit('general', {
    points: 100,
    duration: 15 * 60 // 15 minutes
  })
};
