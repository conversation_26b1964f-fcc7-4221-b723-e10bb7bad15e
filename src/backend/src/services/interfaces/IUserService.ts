import { User, UserSkill, Skill } from '@prisma/client';

export interface CreateUserDto {
  name: string;
  email: string;
}

export interface AddSkillToUserDto {
  skillId: number;
}

export interface UserWithSkills extends User {
  skills: (UserSkill & { skill: Skill })[];
}

export interface IUserService {
  getUsers(): Promise<User[]>;
  getUserById(id: number): Promise<UserWithSkills | null>;
  getUserByEmail(email: string): Promise<User | null>;
  getUserSkills(userId: number): Promise<(UserSkill & { skill: Skill })[]>;
  createUser(data: CreateUserDto): Promise<User>;
  updateUser(id: number, data: Partial<CreateUserDto>): Promise<User>;
  deleteUser(id: number): Promise<void>;
  addSkillToUser(userId: number, data: AddSkillToUserDto): Promise<UserSkill>;
  removeSkillFromUser(userId: number, skillId: number): Promise<void>;
}
