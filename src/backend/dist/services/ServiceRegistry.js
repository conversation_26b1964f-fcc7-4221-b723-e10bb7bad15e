"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceRegistry = void 0;
const implementations_1 = require("./implementations");
const DependencyContainer_1 = require("./DependencyContainer");
class ServiceRegistry {
    constructor(prisma) {
        this.container = new DependencyContainer_1.DependencyContainer(prisma);
        this.skillService = new implementations_1.SkillService(this.container.prisma);
        this.userService = new implementations_1.UserService(this.container.prisma);
        this.categoryService = new implementations_1.CategoryService(this.container.prisma);
    }
    static getInstance(prisma) {
        if (!ServiceRegistry.instance) {
            if (!prisma) {
                throw new Error('PrismaClient is required for first initialization');
            }
            ServiceRegistry.instance = new ServiceRegistry(prisma);
        }
        return ServiceRegistry.instance;
    }
    getSkillService() {
        return this.skillService;
    }
    getUserService() {
        return this.userService;
    }
    getCategoryService() {
        return this.categoryService;
    }
    // Method to replace services for testing
    setSkillService(service) {
        this.skillService = service;
    }
    setUserService(service) {
        this.userService = service;
    }
    setCategoryService(service) {
        this.categoryService = service;
    }
}
exports.ServiceRegistry = ServiceRegistry;
