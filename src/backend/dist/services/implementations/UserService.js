"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
class UserService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    getUsers() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.findMany();
        });
    }
    getUserById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.findUnique({
                where: { id },
                include: {
                    skills: {
                        include: {
                            skill: true
                        }
                    }
                }
            });
        });
    }
    getUserByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.findUnique({
                where: { email }
            });
        });
    }
    getUserSkills(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const userSkills = yield this.prisma.userSkill.findMany({
                where: { userId },
                include: {
                    skill: true
                }
            });
            return userSkills;
        });
    }
    createUser(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.create({
                data
            });
        });
    }
    updateUser(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.user.update({
                where: { id },
                data
            });
        });
    }
    deleteUser(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.prisma.user.delete({
                where: { id }
            });
        });
    }
    addSkillToUser(userId, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.userSkill.create({
                data: {
                    userId,
                    skillId: data.skillId
                }
            });
        });
    }
    removeSkillFromUser(userId, skillId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.prisma.userSkill.delete({
                where: {
                    userId_skillId: {
                        userId,
                        skillId
                    }
                }
            });
        });
    }
}
exports.UserService = UserService;
