#!/bin/sh
set -e

echo "🔧 Starting backend service..."

# Wait for database to be ready
echo "⏳ Waiting for database..."
node wait-for-db.js

# Generate Prisma client
echo "📊 Generating Prisma client..."
npx prisma generate

# Run migrations
echo "🔄 Running database migrations..."
npx prisma migrate deploy

# Seed the database in development mode
if [ "$NODE_ENV" = "development" ]; then
  echo "🌱 Seeding database with sample data..."
  npx prisma db seed
fi

# Start the application based on environment
if [ "$NODE_ENV" = "production" ]; then
  echo "🚀 Starting production server..."
  exec npm start
else
  echo "🛠️ Starting development server..."
  exec npm run dev
fi