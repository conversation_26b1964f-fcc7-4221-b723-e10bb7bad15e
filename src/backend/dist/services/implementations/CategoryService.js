"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryService = void 0;
class CategoryService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    getCategories() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.category.findMany();
        });
    }
    getCategoryById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.category.findUnique({
                where: { id }
            });
        });
    }
    createCategory(data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.category.create({
                data
            });
        });
    }
    updateCategory(id, data) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.prisma.category.update({
                where: { id },
                data
            });
        });
    }
    deleteCategory(id) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.prisma.category.delete({
                where: { id }
            });
        });
    }
}
exports.CategoryService = CategoryService;
