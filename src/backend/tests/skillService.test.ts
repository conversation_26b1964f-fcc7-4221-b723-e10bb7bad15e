import { SkillService } from '../src/services/implementations/SkillService';
import { CreateSkillDto } from '../src/services/interfaces';

// Mock Prisma client
const mockPrismaClient = {
  skill: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('SkillService', () => {
  let skillService: SkillService;

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    // @ts-ignore - Using mock for testing
    skillService = new SkillService(mockPrismaClient);
  });

  describe('getSkills', () => {
    it('should return all skills with categories', async () => {
      const mockSkills = [
        {
          id: 1,
          name: 'JavaScript',
          categoryId: 1,
          category: { id: 1, name: 'Programming' }
        },
        {
          id: 2,
          name: 'Python',
          categoryId: 2,
          category: { id: 2, name: 'Data Science' }
        }
      ];

      mockPrismaClient.skill.findMany.mockResolvedValue(mockSkills);

      const result = await skillService.getSkills();

      expect(mockPrismaClient.skill.findMany).toHaveBeenCalledWith({
        include: { category: true }
      });
      expect(result).toEqual(mockSkills);
    });

    it('should handle empty skills list', async () => {
      mockPrismaClient.skill.findMany.mockResolvedValue([]);

      const result = await skillService.getSkills();

      expect(result).toEqual([]);
    });
  });

  describe('getSkillById', () => {
    it('should return skill with category when found', async () => {
      const mockSkill = {
        id: 1,
        name: 'JavaScript',
        categoryId: 1,
        category: { id: 1, name: 'Programming' }
      };

      mockPrismaClient.skill.findUnique.mockResolvedValue(mockSkill);

      const result = await skillService.getSkillById(1);

      expect(mockPrismaClient.skill.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { category: true }
      });
      expect(result).toEqual(mockSkill);
    });

    it('should return null when skill not found', async () => {
      mockPrismaClient.skill.findUnique.mockResolvedValue(null);

      const result = await skillService.getSkillById(999);

      expect(result).toBeNull();
    });
  });

  describe('createSkill', () => {
    it('should create a new skill successfully', async () => {
      const skillData: CreateSkillDto = {
        name: 'TypeScript',
        categoryId: 1
      };

      const mockCreatedSkill = {
        id: 3,
        name: 'TypeScript',
        categoryId: 1
      };

      mockPrismaClient.skill.create.mockResolvedValue(mockCreatedSkill);

      const result = await skillService.createSkill(skillData);

      expect(mockPrismaClient.skill.create).toHaveBeenCalledWith({
        data: skillData
      });
      expect(result).toEqual(mockCreatedSkill);
    });

    it('should handle creation errors', async () => {
      const skillData: CreateSkillDto = {
        name: 'Invalid Skill',
        categoryId: 999
      };

      mockPrismaClient.skill.create.mockRejectedValue(new Error('Foreign key constraint failed'));

      await expect(skillService.createSkill(skillData)).rejects.toThrow('Foreign key constraint failed');
    });
  });

  describe('updateSkill', () => {
    it('should update skill successfully', async () => {
      const updateData = { name: 'Updated JavaScript' };
      const mockUpdatedSkill = {
        id: 1,
        name: 'Updated JavaScript',
        categoryId: 1
      };

      mockPrismaClient.skill.update.mockResolvedValue(mockUpdatedSkill);

      const result = await skillService.updateSkill(1, updateData);

      expect(mockPrismaClient.skill.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData
      });
      expect(result).toEqual(mockUpdatedSkill);
    });

    it('should handle update errors for non-existent skill', async () => {
      mockPrismaClient.skill.update.mockRejectedValue(new Error('Record not found'));

      await expect(skillService.updateSkill(999, { name: 'New Name' })).rejects.toThrow('Record not found');
    });
  });

  describe('deleteSkill', () => {
    it('should delete skill successfully', async () => {
      mockPrismaClient.skill.delete.mockResolvedValue({});

      await skillService.deleteSkill(1);

      expect(mockPrismaClient.skill.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });

    it('should handle deletion errors for non-existent skill', async () => {
      mockPrismaClient.skill.delete.mockRejectedValue(new Error('Record not found'));

      await expect(skillService.deleteSkill(999)).rejects.toThrow('Record not found');
    });
  });
});
