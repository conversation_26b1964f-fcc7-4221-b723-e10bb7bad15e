import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function generateDatabaseSummary() {
  try {
    console.log('📊 Generating database summary...');
    
    // Get counts from each table
    const categoryCount = await prisma.category.count();
    const skillCount = await prisma.skill.count();
    const userCount = await prisma.user.count();
    const userSkillCount = await prisma.userSkill.count();
    
    console.log('\n=========================');
    console.log('📊 DATABASE SUMMARY');
    console.log('=========================');
    console.log(`📂 Categories: ${categoryCount}`);
    console.log(`🛠️ Skills: ${skillCount}`);
    console.log(`👥 Users: ${userCount}`);
    console.log(`🔗 User-Skill Relationships: ${userSkillCount}`);
    console.log('=========================');
    
    // Top categories by skill count
    const categoriesWithSkillCount = await prisma.category.findMany({
      include: {
        _count: {
          select: { skills: true }
        }
      },
      orderBy: {
        skills: {
          _count: 'desc'
        }
      }
    });
    
    console.log('\n📊 TOP CATEGORIES BY SKILL COUNT:');
    categoriesWithSkillCount.forEach((category, index) => {
      console.log(`${index + 1}. ${category.name}: ${category._count.skills} skills`);
    });
    
    // Top skills by user count
    const skillsWithUserCount = await prisma.skill.findMany({
      include: {
        _count: {
          select: { users: true }
        },
        category: true
      },
      orderBy: {
        users: {
          _count: 'desc'
        }
      },
      take: 10
    });
    
    console.log('\n📊 TOP 10 SKILLS BY USER COUNT:');
    skillsWithUserCount.forEach((skill, index) => {
      console.log(`${index + 1}. ${skill.name} (${skill.category.name}): ${skill._count.users} users`);
    });
    
    // Top users by skill count
    const usersWithSkillCount = await prisma.user.findMany({
      include: {
        _count: {
          select: { skills: true }
        }
      },
      orderBy: {
        skills: {
          _count: 'desc'
        }
      },
      take: 10
    });
    
    console.log('\n📊 TOP 10 USERS BY SKILL COUNT:');
    usersWithSkillCount.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}: ${user._count.skills} skills`);
    });
    
    console.log('\n🎉 Database summary generated successfully!');
  } catch (error) {
    console.error('❌ Error generating database summary:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generateDatabaseSummary();