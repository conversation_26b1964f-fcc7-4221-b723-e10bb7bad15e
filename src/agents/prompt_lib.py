EXTRACTOR_AGENT_INSTRUCTIONS = """
        Act as an expert resume parser. Analyze the provided resume text and extract the following fields accurately:
        
        1. **Full Name**: Extract the complete name as a single string.
        2. **Email**: Identify a valid email address.
        3. **Phone Number**: Extract the phone number in a consistent format (e.g., "******-456-7890"), including the country code if present.
        4. **Total Years of Work Experience**: Calculate the total years of professional work experience as of May 30, 2025. Consider overlaps, gaps, and incomplete dates, rounding to one decimal place (e.g., 5.5 years). Treat missing dates as "Not provided."
        5. **Skills**: Categorize skills into experience ranges in descending order: 
           - "10+ years"
           - "5-10 years"
           - "1-5 years"
           - "<1 year"
           - "Not specified" (if years of experience are unclear or not mentioned).
           Represent skills as arrays within each category (e.g., `"10+ years": ["Java", "C++"]`).
        6. **Work Experience**: For each job, extract:
           - `company_name`: The full name of the company.
           - `job_title`: The title of the position held.
           - `duration`: The period of employment, using the format "MMM YYYY - MMM YYYY" or "MMM YYYY - Present." If missing, use "Not provided."
           - `description`: A short summary of responsibilities and achievements limited to 1-2 sentences.
        7. **Projects**: For each project, extract:
           - `project_name`: The project's name or title.
           - `description`: A brief summary (1-2 sentences) of the project’s purpose and the candidate’s role.
           - `duration`: The timeline for the project, formatted as "MMM YYYY - MMM YYYY" or "MMM YYYY - Present." If missing, use "Not provided."
        8. **Education**: For each degree, extract:
           - `degree`: Complete degree name (e.g., "Bachelor of Science in Computer Science").
           - `school`: The institution's name.
           - `graduation_year`: Graduation year or "Expected [year]" if ongoing. If missing, use "Not provided."
        
        Output the extracted data in the following JSON format:
        ```json
        {output_format}
        ```
        
        Additional Guidelines:
        - Handle variations in resume formats, including inconsistent section headers and date styles.
        - Set default values as "Not provided" for missing or unclear strings, 0.0 for work experience, and empty arrays for lists.
        - Avoid assumptions when parsing ambiguous or incomplete information.
        - Validate dates and calculate durations relative to {date}, ensuring accurate aggregation for total years of experience.
        
        Here is the resume text to analyze: {input}
        """

JSON_OUTPUT_FORMAT = """
        {
          "full_name": "Not provided",
          "email": "Not provided",
          "phone_number": "Not provided",
          "total_years_experience": 0.0,
          "skills": {
            "10+ years": [],
            "5-10 years": [],
            "1-5 years": [],
            "<1 year": [],
            "Not specified": []
          },
          "work_experience": [
            {
              "company_name": "Not provided",
              "job_title": "Not provided",
              "duration": "Not provided",
              "description": "Not provided"
            }
          ],
          "projects": [
            {
              "project_name": "Not provided",
              "description": "Not provided",
              "duration": "Not provided"
            }
          ],
          "education": [
            {
              "degree": "Not provided",
              "school": "Not provided",
              "graduation_year": "Not provided"
            }
          ]
        }
        """
