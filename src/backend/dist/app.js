"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const client_1 = __importDefault(require("./prisma/client"));
const services_1 = require("./services");
const userRoutes_1 = require("./routes/userRoutes");
const categoryRoutes_1 = require("./routes/categoryRoutes");
const skillRoutes_1 = require("./routes/skillRoutes");
const swagger_1 = require("./config/swagger");
const middleware_1 = require("./middleware");
const app = (0, express_1.default)();
console.log('🔧 Initializing Express app...');
// Initialize dependency injection
console.log('🔧 Creating service registry...');
const serviceRegistry = services_1.ServiceRegistry.getInstance(client_1.default);
console.log('✅ Service registry created successfully');
// Security middleware
console.log('🔧 Setting up security middleware...');
app.use((0, helmet_1.default)({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));
console.log('✅ Helmet security headers configured');
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
}));
console.log('✅ CORS configured');
// No rate limiting or request logging
// Middleware
app.use(express_1.default.json({ limit: '10mb' }));
console.log('✅ JSON middleware added');
// Swagger documentation
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swagger_1.specs, {
    explorer: true,
    swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true
    }
}));
console.log('✅ Swagger UI configured');
// Routes with dependency injection
console.log('🔧 Setting up API routes...');
try {
    app.use('/api/users', (0, userRoutes_1.createUserRouter)(serviceRegistry));
    console.log('✅ User routes registered');
    app.use('/api/categories', (0, categoryRoutes_1.createCategoryRouter)(serviceRegistry));
    console.log('✅ Category routes registered');
    app.use('/api/skills', (0, skillRoutes_1.createSkillRouter)(serviceRegistry));
    console.log('✅ Skill routes registered');
}
catch (error) {
    console.error('❌ Error setting up routes:', error);
}
// Health check endpoint
app.get('/health', (_req, res) => {
    res.json({ status: 'ok' });
});
console.log('✅ Health endpoint registered');
// API info endpoint
app.get('/api', (_req, res) => {
    res.json({
        message: 'CodePlus Platform API',
        version: '1.0.0',
        documentation: '/api-docs',
        endpoints: {
            users: '/api/users',
            categories: '/api/categories',
            skills: '/api/skills',
            health: '/health'
        }
    });
});
console.log('✅ API info endpoint registered');
// No error logging middleware
// 404 handler for unmatched routes
app.use(middleware_1.notFoundHandler);
console.log('✅ 404 handler registered');
// Global error handler (must be last)
app.use(middleware_1.errorHandler);
exports.default = app;
