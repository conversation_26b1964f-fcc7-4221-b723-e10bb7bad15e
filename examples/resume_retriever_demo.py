#!/usr/bin/env python3
"""
Resume Retriever Demo

This script demonstrates the usage of the ResumeRetriever class for semantic search
over resume data stored in PostgreSQL with PGVector.
"""

import sys
import os
import json

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.resume_retriever import (
    ResumeRetriever,
    create_skill_retriever,
    create_work_experience_retriever,
    create_project_retriever,
    create_user_retriever
)


def demo_basic_retrieval():
    """Demonstrate basic semantic retrieval functionality."""
    print("🔍 Basic Semantic Retrieval Demo")
    print("=" * 50)
    
    try:
        # Create a general retriever
        retriever = ResumeRetriever(k=3)
        
        # Example queries
        queries = [
            "Python programming skills",
            "machine learning experience",
            "software engineering at tech companies",
            "data science projects"
        ]
        
        for query in queries:
            print(f"\n📝 Query: '{query}'")
            try:
                docs = retriever.invoke(query)
                print(f"📊 Found {len(docs)} documents")
                
                for i, doc in enumerate(docs, 1):
                    metadata = doc.metadata
                    print(f"  {i}. {metadata.get('full_name', 'Unknown')} - "
                          f"{metadata.get('chunk_type', 'Unknown')} - "
                          f"Score: {metadata.get('similarity_score', 0):.3f}")
                    print(f"     Content: {doc.page_content[:100]}...")
                    
            except Exception as e:
                print(f"❌ Error with query '{query}': {str(e)}")
        
        print("\n✅ Basic retrieval demo completed")
        
    except Exception as e:
        print(f"❌ Failed to initialize retriever: {str(e)}")


def demo_filtered_retrieval():
    """Demonstrate filtered retrieval with specific criteria."""
    print("\n🎯 Filtered Retrieval Demo")
    print("=" * 50)
    
    try:
        # Demo 1: Skills with experience level filter
        print("\n1️⃣ Skills with 5-10 years experience:")
        skill_retriever = create_skill_retriever(experience_level="5-10 years", k=3)
        docs = skill_retriever.invoke("Python Django PostgreSQL")
        
        for doc in docs:
            metadata = doc.metadata
            print(f"  • {metadata.get('full_name', 'Unknown')}: {doc.page_content[:80]}...")
        
        # Demo 2: Work experience at specific companies
        print("\n2️⃣ Work experience at tech companies:")
        work_retriever = create_work_experience_retriever(k=3)
        docs = work_retriever.invoke("software engineer at technology companies")
        
        for doc in docs:
            metadata = doc.metadata
            print(f"  • {metadata.get('full_name', 'Unknown')}: {doc.page_content[:80]}...")
        
        # Demo 3: Projects related to AI/ML
        print("\n3️⃣ AI/ML related projects:")
        project_retriever = create_project_retriever(k=3)
        docs = project_retriever.invoke("artificial intelligence machine learning data science")
        
        for doc in docs:
            metadata = doc.metadata
            print(f"  • {metadata.get('full_name', 'Unknown')}: {doc.page_content[:80]}...")
        
        print("\n✅ Filtered retrieval demo completed")
        
    except Exception as e:
        print(f"❌ Filtered retrieval demo failed: {str(e)}")


def demo_user_specific_retrieval():
    """Demonstrate user-specific retrieval."""
    print("\n👤 User-Specific Retrieval Demo")
    print("=" * 50)
    
    try:
        # First, get a list of available users
        general_retriever = ResumeRetriever(k=10)
        docs = general_retriever.invoke("software engineer")
        
        if docs:
            # Get the first user for demo
            first_user_id = docs[0].metadata.get('user_id')
            first_user_name = docs[0].metadata.get('full_name', 'Unknown')
            
            print(f"📋 Retrieving all information for user: {first_user_name} ({first_user_id})")
            
            # Create user-specific retriever
            user_retriever = create_user_retriever(user_id=first_user_id, k=20)
            user_docs = user_retriever.invoke("all skills experience projects")
            
            # Group by chunk type
            skills = []
            work_exp = []
            projects = []
            
            for doc in user_docs:
                chunk_type = doc.metadata.get('chunk_type', 'unknown')
                if chunk_type == 'skill_group':
                    skills.append(doc.page_content)
                elif chunk_type == 'work_experience':
                    work_exp.append(doc.page_content)
                elif chunk_type == 'project':
                    projects.append(doc.page_content)
            
            print(f"\n🔧 Skills ({len(skills)} items):")
            for skill in skills[:3]:  # Show first 3
                print(f"  • {skill[:100]}...")
            
            print(f"\n💼 Work Experience ({len(work_exp)} items):")
            for exp in work_exp[:3]:  # Show first 3
                print(f"  • {exp[:100]}...")
            
            print(f"\n🚀 Projects ({len(projects)} items):")
            for project in projects[:3]:  # Show first 3
                print(f"  • {project[:100]}...")
        
        else:
            print("No users found in the database")
        
        print("\n✅ User-specific retrieval demo completed")
        
    except Exception as e:
        print(f"❌ User-specific retrieval demo failed: {str(e)}")


def demo_advanced_filtering():
    """Demonstrate advanced filtering capabilities."""
    print("\n⚙️ Advanced Filtering Demo")
    print("=" * 50)
    
    try:
        # Demo 1: High similarity threshold
        print("\n1️⃣ High precision search (similarity > 0.7):")
        precise_retriever = ResumeRetriever(similarity_threshold=0.7, k=5)
        docs = precise_retriever.invoke("Python machine learning artificial intelligence")
        
        print(f"Found {len(docs)} highly relevant documents")
        for doc in docs:
            metadata = doc.metadata
            print(f"  • Score: {metadata.get('similarity_score', 0):.3f} - "
                  f"{metadata.get('full_name', 'Unknown')}")
        
        # Demo 2: Metadata filtering
        print("\n2️⃣ Metadata-based filtering:")
        metadata_retriever = ResumeRetriever(
            metadata_filters={"experience_level": "senior"},
            k=5
        )
        docs = metadata_retriever.invoke("leadership team management")
        
        print(f"Found {len(docs)} documents with senior experience level")
        for doc in docs:
            metadata = doc.metadata
            print(f"  • {metadata.get('full_name', 'Unknown')}: {doc.page_content[:80]}...")
        
        # Demo 3: Combined filters
        print("\n3️⃣ Combined filtering (skills + experience level + high similarity):")
        combined_retriever = ResumeRetriever(
            chunk_type_filter="skill_group",
            chunk_subtype_filter="10+ years",
            similarity_threshold=0.6,
            k=3
        )
        docs = combined_retriever.invoke("senior software architect system design")
        
        print(f"Found {len(docs)} documents matching all criteria")
        for doc in docs:
            metadata = doc.metadata
            print(f"  • Score: {metadata.get('similarity_score', 0):.3f} - "
                  f"{metadata.get('full_name', 'Unknown')}: {doc.page_content[:60]}...")
        
        print("\n✅ Advanced filtering demo completed")
        
    except Exception as e:
        print(f"❌ Advanced filtering demo failed: {str(e)}")


def main():
    """Run all demo functions."""
    print("🚀 Resume Retriever Comprehensive Demo")
    print("=" * 60)
    print("This demo showcases the semantic search capabilities of the ResumeRetriever")
    print("for querying resume data stored in PostgreSQL with PGVector.")
    print()
    
    # Check if we can connect to the database
    try:
        test_retriever = ResumeRetriever(k=1)
        test_docs = test_retriever.invoke("test connection")
        print(f"✅ Database connection successful! Found {len(test_docs)} documents.")
        print()
        
        # Run all demos
        demo_basic_retrieval()
        demo_filtered_retrieval()
        demo_user_specific_retrieval()
        demo_advanced_filtering()
        
        print("\n🎉 All demos completed successfully!")
        print("\nNext steps:")
        print("  1. Try your own queries with different filters")
        print("  2. Integrate with LangChain agents and chains")
        print("  3. Build RAG applications for HR and talent management")
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print("\nTroubleshooting:")
        print("  1. Check your PostgreSQL connection settings")
        print("  2. Ensure the people_skill_set table exists and has data")
        print("  3. Verify your OpenAI API key is set")
        print("  4. Check that PGVector extension is installed")


if __name__ == "__main__":
    main()
