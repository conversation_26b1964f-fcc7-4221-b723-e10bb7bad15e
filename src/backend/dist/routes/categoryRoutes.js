"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCategoryRouter = exports.CategoryRouteFactory = void 0;
const express_1 = require("express");
const categoryController_1 = require("../controllers/categoryController");
const middleware_1 = require("../middleware");
class CategoryRouteFactory {
    createRouter(serviceRegistry) {
        const router = (0, express_1.Router)();
        const categoryController = new categoryController_1.CategoryController(serviceRegistry);
        // GET /categories - Get all categories
        router.get('/', categoryController.getCategories);
        // GET /categories/:id - Get category by ID
        router.get('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), categoryController.getCategoryById);
        // POST /categories - Create new category
        router.post('/', (0, middleware_1.validateFields)(middleware_1.categoryValidationRules), categoryController.createCategory);
        // PUT /categories/:id - Update category
        router.put('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), (0, middleware_1.validateFields)(middleware_1.categoryValidationRules.map((rule) => (Object.assign(Object.assign({}, rule), { required: false })))), categoryController.updateCategory);
        // DELETE /categories/:id - Delete category
        router.delete('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), categoryController.deleteCategory);
        return router;
    }
}
exports.CategoryRouteFactory = CategoryRouteFactory;
// Factory function for backward compatibility
const createCategoryRouter = (serviceRegistry) => {
    const factory = new CategoryRouteFactory();
    return factory.createRouter(serviceRegistry);
};
exports.createCategoryRouter = createCategoryRouter;
// Legacy export for existing code - will be deprecated
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.json({ message: 'Categories endpoint requires proper initialization. Please use the new dependency injection system.' });
});
exports.default = router;
