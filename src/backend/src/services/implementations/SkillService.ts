import { PrismaClient } from '@prisma/client';
import { ISkillService, CreateSkillDto, SkillWithCategory } from '../interfaces';

export class SkillService implements ISkillService {
  constructor(private readonly prisma: PrismaClient) {}

  async getSkills(): Promise<SkillWithCategory[]> {
    return await this.prisma.skill.findMany({
      include: { category: true }
    });
  }

  async getSkillById(id: number): Promise<SkillWithCategory | null> {
    return await this.prisma.skill.findUnique({
      where: { id },
      include: { category: true }
    });
  }

  async createSkill(data: CreateSkillDto) {
    return await this.prisma.skill.create({
      data
    });
  }

  async updateSkill(id: number, data: Partial<CreateSkillDto>) {
    return await this.prisma.skill.update({
      where: { id },
      data
    });
  }

  async deleteSkill(id: number): Promise<void> {
    await this.prisma.skill.delete({
      where: { id }
    });
  }
}
