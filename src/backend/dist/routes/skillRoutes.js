"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSkillRouter = exports.SkillRouteFactory = void 0;
const express_1 = require("express");
const skillController_1 = require("../controllers/skillController");
const middleware_1 = require("../middleware");
class SkillRouteFactory {
    createRouter(serviceRegistry) {
        const router = (0, express_1.Router)();
        const skillController = new skillController_1.SkillController(serviceRegistry);
        // GET /skills - Get all skills
        router.get('/', skillController.getSkills);
        // GET /skills/:id - Get skill by ID
        router.get('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), skillController.getSkillById);
        // POST /skills - Create new skill
        router.post('/', (0, middleware_1.validateFields)(middleware_1.skillValidationRules), skillController.createSkill);
        // PUT /skills/:id - Update skill
        router.put('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), (0, middleware_1.validateFields)(middleware_1.skillValidationRules.map((rule) => (Object.assign(Object.assign({}, rule), { required: false })))), skillController.updateSkill);
        // DELETE /skills/:id - Delete skill
        router.delete('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), skillController.deleteSkill);
        return router;
    }
}
exports.SkillRouteFactory = SkillRouteFactory;
// Factory function for backward compatibility
const createSkillRouter = (serviceRegistry) => {
    const factory = new SkillRouteFactory();
    return factory.createRouter(serviceRegistry);
};
exports.createSkillRouter = createSkillRouter;
// Legacy export for existing code - will be deprecated
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.json({ message: 'Skills endpoint requires proper initialization. Please use the new dependency injection system.' });
});
exports.default = router;
