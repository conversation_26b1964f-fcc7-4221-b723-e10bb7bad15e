"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addSkillToUserValidationRules = exports.categoryValidationRules = exports.skillValidationRules = exports.userValidationRules = exports.validateParams = exports.validateFields = exports.ValidationError = void 0;
class ValidationError extends Error {
    constructor(field, message) {
        super(`Validation failed for field '${field}': ${message}`);
        this.field = field;
        this.message = message;
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
const validateFields = (rules) => {
    return (req, res, next) => {
        const errors = [];
        for (const rule of rules) {
            const value = req.body[rule.field];
            // Check required fields
            if (rule.required && (value === undefined || value === null || value === '')) {
                errors.push(`${rule.field} is required`);
                continue;
            }
            // Skip further validation if field is not present and not required
            if (value === undefined || value === null) {
                continue;
            }
            // Type validation
            if (rule.type) {
                switch (rule.type) {
                    case 'string':
                        if (typeof value !== 'string') {
                            errors.push(`${rule.field} must be a string`);
                        }
                        break;
                    case 'number':
                        if (typeof value !== 'number' && isNaN(Number(value))) {
                            errors.push(`${rule.field} must be a number`);
                        }
                        break;
                    case 'email':
                        if (typeof value !== 'string' || !isValidEmail(value)) {
                            errors.push(`${rule.field} must be a valid email address`);
                        }
                        break;
                    case 'boolean':
                        if (typeof value !== 'boolean') {
                            errors.push(`${rule.field} must be a boolean`);
                        }
                        break;
                }
            }
            // String length validation
            if (typeof value === 'string') {
                if (rule.minLength && value.length < rule.minLength) {
                    errors.push(`${rule.field} must be at least ${rule.minLength} characters long`);
                }
                if (rule.maxLength && value.length > rule.maxLength) {
                    errors.push(`${rule.field} must be at most ${rule.maxLength} characters long`);
                }
            }
            // Number range validation
            if (typeof value === 'number' || !isNaN(Number(value))) {
                const numValue = Number(value);
                if (rule.min !== undefined && numValue < rule.min) {
                    errors.push(`${rule.field} must be at least ${rule.min}`);
                }
                if (rule.max !== undefined && numValue > rule.max) {
                    errors.push(`${rule.field} must be at most ${rule.max}`);
                }
            }
        }
        if (errors.length > 0) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors
            });
            return;
        }
        next();
    };
};
exports.validateFields = validateFields;
const validateParams = (paramRules) => {
    return (req, res, next) => {
        const errors = [];
        for (const rule of paramRules) {
            const value = req.params[rule.param];
            if (!value) {
                errors.push(`${rule.param} parameter is required`);
                continue;
            }
            if (rule.type === 'number') {
                if (isNaN(Number(value))) {
                    errors.push(`${rule.param} must be a valid number`);
                }
                else if (Number(value) <= 0) {
                    errors.push(`${rule.param} must be a positive number`);
                }
            }
        }
        if (errors.length > 0) {
            res.status(400).json({
                error: 'Parameter validation failed',
                details: errors
            });
            return;
        }
        next();
    };
};
exports.validateParams = validateParams;
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
// Common validation rules
exports.userValidationRules = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'email', required: true, type: 'email', maxLength: 255 }
];
exports.skillValidationRules = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'description', type: 'string', maxLength: 500 },
    { field: 'categoryId', required: true, type: 'number', min: 1 }
];
exports.categoryValidationRules = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'description', type: 'string', maxLength: 500 }
];
exports.addSkillToUserValidationRules = [
    { field: 'skillId', required: true, type: 'number', min: 1 }
];
