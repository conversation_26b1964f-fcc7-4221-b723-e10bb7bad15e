# Staging environment configuration
# This file should be created on your staging server

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=staging_password_change_me
POSTGRES_DB=agent_service_staging

# AI Provider API Keys
OPENAI_API_KEY=sk-staging-openai-key
ANTHROPIC_API_KEY=staging-anthropic-key
GROQ_API_KEY=staging-groq-key

# LangSmith
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=staging-langsmith-key
LANGCHAIN_PROJECT=agent-service-toolkit-staging

# Application Settings
LOG_LEVEL=DEBUG
DEBUG=true

# Security
SECRET_KEY=staging-secret-key-change-me

# Deployment
VERSION=main
