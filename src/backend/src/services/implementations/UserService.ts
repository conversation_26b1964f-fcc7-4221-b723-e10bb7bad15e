import { PrismaClient } from '@prisma/client';
import { IUserService, CreateUserDto, AddSkillToUserDto, UserWithSkills } from '../interfaces';

export class UserService implements IUserService {
  constructor(private readonly prisma: PrismaClient) {}

  async getUsers() {
    return await this.prisma.user.findMany();
  }  async getUserById(id: number): Promise<UserWithSkills | null> {
    return await this.prisma.user.findUnique({
      where: { id },
      include: {
        skills: {
          include: {
            skill: true
          }
        }
      }
    });
  }

  async getUserByEmail(email: string) {
    return await this.prisma.user.findUnique({
      where: { email }
    });
  }

  async getUserSkills(userId: number) {
    const userSkills = await this.prisma.userSkill.findMany({
      where: { userId },
      include: {
        skill: true
      }
    });
    return userSkills;
  }

  async createUser(data: CreateUserDto) {
    return await this.prisma.user.create({
      data
    });
  }  async updateUser(id: number, data: Partial<CreateUserDto>) {
    return await this.prisma.user.update({
      where: { id },
      data
    });
  }

  async deleteUser(id: number): Promise<void> {
    await this.prisma.user.delete({
      where: { id }
    });
  }

  async addSkillToUser(userId: number, data: AddSkillToUserDto) {
    return await this.prisma.userSkill.create({
      data: {
        userId,
        skillId: data.skillId
      }
    });
  }

  async removeSkillFromUser(userId: number, skillId: number): Promise<void> {
    await this.prisma.userSkill.delete({
      where: {
        userId_skillId: {
          userId,
          skillId
        }
      }
    });
  }
}
