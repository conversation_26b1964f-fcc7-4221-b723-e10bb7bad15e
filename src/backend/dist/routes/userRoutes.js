"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserRouter = exports.UserRouteFactory = void 0;
const express_1 = require("express");
const userController_1 = require("../controllers/userController");
const middleware_1 = require("../middleware");
class UserRouteFactory {
    createRouter(serviceRegistry) {
        const router = (0, express_1.Router)();
        const userController = new userController_1.UserController(serviceRegistry);
        // GET /users - Get all users
        router.get('/', userController.getUsers);
        // GET /users/:id - Get user by ID
        router.get('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), userController.getUserById);
        // GET /users/:userId/skills - Get user skills
        router.get('/:userId/skills', (0, middleware_1.validateParams)([{ param: 'userId', type: 'number' }]), userController.getUserSkills);
        // POST /users - Create new user
        router.post('/', (0, middleware_1.validateFields)(middleware_1.userValidationRules), userController.createUser);
        // PUT /users/:id - Update user
        router.put('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), (0, middleware_1.validateFields)(middleware_1.userValidationRules.map((rule) => (Object.assign(Object.assign({}, rule), { required: false })))), userController.updateUser);
        // DELETE /users/:id - Delete user
        router.delete('/:id', (0, middleware_1.validateParams)([{ param: 'id', type: 'number' }]), userController.deleteUser);
        // POST /users/:userId/skills - Add skill to user
        router.post('/:userId/skills', (0, middleware_1.validateParams)([{ param: 'userId', type: 'number' }]), (0, middleware_1.validateFields)(middleware_1.addSkillToUserValidationRules), userController.addSkillToUser);
        // DELETE /users/:userId/skills/:skillId - Remove skill from user
        router.delete('/:userId/skills/:skillId', (0, middleware_1.validateParams)([
            { param: 'userId', type: 'number' },
            { param: 'skillId', type: 'number' }
        ]), userController.removeSkillFromUser);
        return router;
    }
}
exports.UserRouteFactory = UserRouteFactory;
// Factory function for backward compatibility
const createUserRouter = (serviceRegistry) => {
    const factory = new UserRouteFactory();
    return factory.createRouter(serviceRegistry);
};
exports.createUserRouter = createUserRouter;
// Legacy export for existing code - will be deprecated
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.json({ message: 'Users endpoint requires proper initialization. Please use the new dependency injection system.' });
});
exports.default = router;
