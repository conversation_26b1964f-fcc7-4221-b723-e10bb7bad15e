#!/bin/bash

# PathForge AI Frontend Deployment Script
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_NAME="pathforge-ai"
COMPOSE_FILE="docker-compose.swarm.yml"
REGISTRY="dockerhub.csharpp.com"

# Default values
VERSION="${VERSION:-latest}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --stack-name)
            STACK_NAME="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -v, --version VERSION     Docker image version (default: latest)"
            echo "  -e, --environment ENV     Environment (staging/production) (default: production)"
            echo "  --stack-name NAME         Docker stack name (default: pathforge-ai)"
            echo "  -h, --help               Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --version 1.2.3"
            echo "  $0 --environment staging"
            echo "  $0 --version 1.2.3 --environment production"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "Environment must be 'staging' or 'production'"
    exit 1
fi

log_info "Starting PathForge AI deployment..."
log_info "Version: $VERSION"
log_info "Environment: $ENVIRONMENT"
log_info "Stack: $STACK_NAME"
log_info "Registry: $REGISTRY"

# Check if Docker Swarm is initialized
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    log_error "Docker Swarm is not initialized"
    log_info "Run: docker swarm init"
    exit 1
fi

# Check if compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Set environment variables for compose
export VERSION="$VERSION"
export ENVIRONMENT="$ENVIRONMENT"

# Deploy the stack
log_info "Deploying stack with version $VERSION..."

if docker stack deploy -c "$COMPOSE_FILE" "$STACK_NAME"; then
    log_success "Stack deployment initiated successfully"
else
    log_error "Stack deployment failed"
    exit 1
fi

# Wait for services to be ready
log_info "Waiting for services to be ready..."
sleep 10

# Check service status
log_info "Checking service status..."
docker stack services "$STACK_NAME"

# Wait for all services to be running
log_info "Waiting for all services to converge..."
for i in {1..30}; do
    if docker stack services "$STACK_NAME" --format "table {{.Replicas}}" | grep -q "0/"; then
        log_warning "Some services are still starting... (attempt $i/30)"
        sleep 10
    else
        log_success "All services are running!"
        break
    fi
    
    if [[ $i -eq 30 ]]; then
        log_warning "Timeout waiting for services to start"
        log_info "Current service status:"
        docker stack services "$STACK_NAME"
        exit 1
    fi
done

# Display final status
log_success "Deployment completed successfully!"

# Send webhook notifications to Portainer
log_info "Sending webhook notifications to Portainer..."
webhook_script="$(dirname "$0")/notify-portainer.sh"
if [ -f "$webhook_script" ]; then
    if "$webhook_script" all "$VERSION"; then
        log_success "Portainer webhook notifications sent successfully"
    else
        log_warning "Some webhook notifications failed, but deployment was successful"
    fi
else
    log_warning "Webhook notification script not found, skipping Portainer webhooks"
fi

log_info "Access the application at:"
log_info "  Frontend: https://pathforge-ai.csharpp.com"
log_info "  Backend API: https://pathforge-ai-backend.csharpp.com"
log_info "  Streamlit App: https://pathforge_ai-streamlit.csharpp.com"

log_info "To monitor the deployment:"
log_info "  docker stack services $STACK_NAME"
log_info "  docker service logs $STACK_NAME_pathforge_ai_frontend"
log_info "  docker service logs $STACK_NAME_pathforge_ai_agent_service"
log_info "  docker service logs $STACK_NAME_pathforge_ai_streamlit_app"
