import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { MantineProvider } from '@mantine/core';
import { GoalDetailPage } from './GoalDetailPage';

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ goalId: 'test-goal-123' }),
  };
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineProvider>
      <MemoryRouter>{component}</MemoryRouter>
    </MantineProvider>
  );
};

describe('GoalDetailPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders the page title and goal ID', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Goal Details')).toBeInTheDocument();
    expect(screen.getByText('Goal ID: test-goal-123')).toBeInTheDocument();
  });

  it('displays development alert', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Page Under Development')).toBeInTheDocument();
    expect(screen.getByText(/This is a temporary goal detail page/)).toBeInTheDocument();
  });

  it('shows navigation buttons', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Back to Home')).toBeInTheDocument();
    expect(screen.getByText('View All Goals')).toBeInTheDocument();
  });

  it('displays feature list', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText("What's Coming Next")).toBeInTheDocument();
    expect(screen.getByText(/Detailed goal information and description/)).toBeInTheDocument();
    expect(screen.getByText(/Learning path breakdown and milestones/)).toBeInTheDocument();
  });

  it('shows navigation flow status', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Navigation Flow Established')).toBeInTheDocument();
    expect(screen.getByText(/Users can now be automatically redirected/)).toBeInTheDocument();
  });
});
