"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["DEBUG"] = "debug";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor() {
        this.requestIdCounter = 0;
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    generateRequestId() {
        return `req-${Date.now()}-${++this.requestIdCounter}`;
    }
    formatLog(data) {
        return JSON.stringify(data, null, process.env.NODE_ENV === 'development' ? 2 : 0);
    }
    log(level, message, metadata, error) {
        const logData = {
            level,
            message,
            timestamp: new Date().toISOString(),
            metadata,
            error: error ? {
                name: error.name,
                message: error.message,
                stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
            } : undefined
        };
        console.log(this.formatLog(logData));
    }
    error(message, error, metadata) {
        this.log(LogLevel.ERROR, message, metadata, error);
    }
    warn(message, metadata) {
        this.log(LogLevel.WARN, message, metadata);
    }
    info(message, metadata) {
        this.log(LogLevel.INFO, message, metadata);
    }
    debug(message, metadata) {
        if (process.env.NODE_ENV === 'development') {
            this.log(LogLevel.DEBUG, message, metadata);
        }
    }
    /**
     * Express middleware for request logging
     */
    requestLogger() {
        return (req, res, next) => {
            const requestId = this.generateRequestId();
            const startTime = Date.now();
            // Add request ID to request object
            req.requestId = requestId; // Log incoming request
            this.info('Incoming request', {
                requestId,
                method: req.method,
                url: req.url,
                ip: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent')
            }); // Override res.end to log response
            const originalEnd = res.end.bind(res);
            res.end = function (chunk, encoding, cb) {
                const duration = Date.now() - startTime;
                Logger.getInstance().info('Request completed', {
                    requestId,
                    method: req.method,
                    url: req.url,
                    statusCode: res.statusCode,
                    duration
                });
                return originalEnd(chunk, encoding, cb);
            };
            next();
        };
    }
    /**
     * Express middleware for error logging
     */
    errorLogger() {
        return (error, req, res, next) => {
            this.error('Request error', error, {
                requestId: req.requestId,
                method: req.method,
                url: req.url,
                statusCode: error.statusCode || 500,
                ip: req.ip || req.connection.remoteAddress
            });
            next(error);
        };
    }
}
exports.Logger = Logger;
// Create singleton instance
exports.logger = Logger.getInstance();
