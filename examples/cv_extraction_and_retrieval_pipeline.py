#!/usr/bin/env python3
"""
CV Extraction and Retrieval Pipeline Demo

This script demonstrates the complete pipeline from CV extraction to semantic retrieval,
showing how the CV extractor and resume retriever work together.
"""

import sys
import os
import json

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.cv_extractor import process_cv_extraction
from agents.resume_retriever import ResumeRetriever, create_skill_retriever


def demo_cv_extraction_and_storage():
    """Demonstrate CV extraction with database storage."""
    print("📄 CV Extraction and Storage Demo")
    print("=" * 50)
    
    # Sample CV text
    sample_cv = """
    Sarah Johnson
    Email: <EMAIL>
    Phone: ******-0123
    
    EXPERIENCE:
    Senior Data Scientist at TechCorp (Jan 2020 - Present)
    • Led machine learning initiatives for customer segmentation
    • Developed predictive models using Python, TensorFlow, and scikit-learn
    • Managed a team of 4 data scientists
    • Improved model accuracy by 35% through feature engineering
    
    Data Analyst at StartupXYZ (Jun 2018 - Dec 2019)
    • Built data pipelines using Python and SQL
    • Created dashboards with Tableau and Power BI
    • Analyzed customer behavior patterns
    
    SKILLS:
    Programming: Python (5+ years), R (3 years), SQL (6 years)
    Machine Learning: TensorFlow, PyTorch, scikit-learn, XGBoost
    Data Visualization: Tableau, Power BI, matplotlib, seaborn
    Cloud: AWS, Google Cloud Platform
    
    PROJECTS:
    Customer Churn Prediction Model (2023)
    • Developed ML model to predict customer churn with 92% accuracy
    • Used ensemble methods and feature selection techniques
    • Deployed model to production using Docker and Kubernetes
    
    Real-time Recommendation System (2022)
    • Built recommendation engine for e-commerce platform
    • Implemented collaborative filtering and content-based algorithms
    • Processed 1M+ user interactions daily
    
    EDUCATION:
    Master of Science in Data Science
    University of Technology, 2018
    
    Bachelor of Science in Computer Science
    State University, 2016
    """
    
    try:
        print("🔄 Extracting CV data...")
        
        # Extract CV data and store in database
        extracted_data = process_cv_extraction(
            sample_cv,
            store_in_db=True,
            user_id="sarah_johnson_demo",
            source_id="demo_cv_2024"
        )
        
        print("✅ CV extraction completed!")
        print(f"📊 Extracted data for: {extracted_data.get('full_name', 'Unknown')}")
        print(f"📧 Email: {extracted_data.get('email', 'Unknown')}")
        print(f"📞 Phone: {extracted_data.get('phone_number', 'Unknown')}")
        print(f"⏱️ Total experience: {extracted_data.get('total_years_experience', 0)} years")
        
        # Show skills breakdown
        skills = extracted_data.get('skills', {})
        print("\n🔧 Skills by experience level:")
        for level, skill_list in skills.items():
            if skill_list:
                print(f"  {level}: {', '.join(skill_list[:3])}{'...' if len(skill_list) > 3 else ''}")
        
        # Show work experience
        work_exp = extracted_data.get('work_experience', [])
        print(f"\n💼 Work Experience ({len(work_exp)} entries):")
        for exp in work_exp[:2]:  # Show first 2
            print(f"  • {exp.get('job_title', 'Unknown')} at {exp.get('company_name', 'Unknown')}")
        
        # Show projects
        projects = extracted_data.get('projects', [])
        print(f"\n🚀 Projects ({len(projects)} entries):")
        for project in projects[:2]:  # Show first 2
            print(f"  • {project.get('project_name', 'Unknown')}")
        
        return extracted_data
        
    except Exception as e:
        print(f"❌ CV extraction failed: {str(e)}")
        return None


def demo_semantic_retrieval(user_id="sarah_johnson_demo"):
    """Demonstrate semantic retrieval of the stored CV data."""
    print(f"\n🔍 Semantic Retrieval Demo for {user_id}")
    print("=" * 50)
    
    try:
        # Wait a moment for database to be updated
        import time
        time.sleep(2)
        
        # Create retrievers
        general_retriever = ResumeRetriever(k=5)
        skill_retriever = create_skill_retriever(k=5)
        
        # Test queries
        queries = [
            ("General search", general_retriever, "machine learning data science Python"),
            ("Skill search", skill_retriever, "Python TensorFlow machine learning"),
            ("Experience search", general_retriever, "team leadership management"),
            ("Project search", general_retriever, "recommendation system churn prediction")
        ]
        
        for query_name, retriever, query_text in queries:
            print(f"\n📝 {query_name}: '{query_text}'")
            
            try:
                docs = retriever.invoke(query_text)
                relevant_docs = [doc for doc in docs if user_id in doc.metadata.get('user_id', '')]
                
                if relevant_docs:
                    print(f"✅ Found {len(relevant_docs)} relevant documents")
                    for doc in relevant_docs[:2]:  # Show first 2
                        metadata = doc.metadata
                        print(f"  • Type: {metadata.get('chunk_type', 'Unknown')} - "
                              f"Score: {metadata.get('similarity_score', 0):.3f}")
                        print(f"    Content: {doc.page_content[:100]}...")
                else:
                    print("❌ No relevant documents found (data may not be indexed yet)")
                    
            except Exception as e:
                print(f"❌ Query failed: {str(e)}")
        
        print("\n✅ Semantic retrieval demo completed")
        
    except Exception as e:
        print(f"❌ Semantic retrieval demo failed: {str(e)}")


def demo_cross_user_search():
    """Demonstrate searching across multiple users."""
    print("\n🌐 Cross-User Search Demo")
    print("=" * 50)
    
    try:
        # Create retrievers for different search types
        skill_retriever = create_skill_retriever(experience_level="5+ years", k=10)
        general_retriever = ResumeRetriever(k=10)
        
        # Search for specific skills across all users
        search_queries = [
            ("Python experts", skill_retriever, "Python programming development"),
            ("Machine learning specialists", general_retriever, "machine learning AI artificial intelligence"),
            ("Team leaders", general_retriever, "team leadership management senior"),
            ("Data scientists", general_retriever, "data science analytics statistics")
        ]
        
        for search_name, retriever, query in search_queries:
            print(f"\n🔍 {search_name}: '{query}'")
            
            try:
                docs = retriever.invoke(query)
                
                if docs:
                    # Group by user
                    users_found = {}
                    for doc in docs:
                        user_name = doc.metadata.get('full_name', 'Unknown')
                        user_id = doc.metadata.get('user_id', 'Unknown')
                        score = doc.metadata.get('similarity_score', 0)
                        
                        if user_name not in users_found or score > users_found[user_name]['score']:
                            users_found[user_name] = {
                                'user_id': user_id,
                                'score': score,
                                'content': doc.page_content[:80]
                            }
                    
                    print(f"✅ Found {len(users_found)} relevant users:")
                    for user_name, info in list(users_found.items())[:3]:  # Show top 3
                        print(f"  • {user_name} (Score: {info['score']:.3f})")
                        print(f"    {info['content']}...")
                else:
                    print("❌ No relevant users found")
                    
            except Exception as e:
                print(f"❌ Search failed: {str(e)}")
        
        print("\n✅ Cross-user search demo completed")
        
    except Exception as e:
        print(f"❌ Cross-user search demo failed: {str(e)}")


def demo_rag_style_qa():
    """Demonstrate RAG-style question answering using the retriever."""
    print("\n🤖 RAG-Style Q&A Demo")
    print("=" * 50)
    
    try:
        retriever = ResumeRetriever(k=5)
        
        questions = [
            "Who has experience with machine learning and Python?",
            "Which candidates have worked at technology companies?",
            "Who has built recommendation systems or AI projects?",
            "Which data scientists have team leadership experience?"
        ]
        
        for question in questions:
            print(f"\n❓ Question: {question}")
            
            try:
                # Retrieve relevant documents
                docs = retriever.invoke(question)
                
                if docs:
                    # Simulate RAG-style response generation
                    print("📋 Retrieved context:")
                    unique_users = set()
                    for doc in docs[:3]:  # Use top 3 documents
                        user_name = doc.metadata.get('full_name', 'Unknown')
                        if user_name not in unique_users:
                            unique_users.add(user_name)
                            print(f"  • {user_name}: {doc.page_content[:100]}...")
                    
                    print(f"💡 Answer: Based on the retrieved data, {len(unique_users)} candidates match your criteria.")
                else:
                    print("❌ No relevant information found")
                    
            except Exception as e:
                print(f"❌ Question processing failed: {str(e)}")
        
        print("\n✅ RAG-style Q&A demo completed")
        
    except Exception as e:
        print(f"❌ RAG-style Q&A demo failed: {str(e)}")


def main():
    """Run the complete pipeline demo."""
    print("🚀 CV Extraction and Retrieval Pipeline Demo")
    print("=" * 60)
    print("This demo shows the complete workflow from CV extraction to semantic retrieval.")
    print()
    
    # Step 1: Extract and store CV data
    extracted_data = demo_cv_extraction_and_storage()
    
    if extracted_data:
        # Step 2: Demonstrate semantic retrieval
        demo_semantic_retrieval()
        
        # Step 3: Cross-user search
        demo_cross_user_search()
        
        # Step 4: RAG-style Q&A
        demo_rag_style_qa()
        
        print("\n🎉 Complete pipeline demo finished!")
        print("\nKey capabilities demonstrated:")
        print("  ✅ CV text extraction and structured data generation")
        print("  ✅ Automatic database storage with embeddings")
        print("  ✅ Semantic search with flexible filtering")
        print("  ✅ Cross-user talent discovery")
        print("  ✅ RAG-style question answering")
        print("\nThis pipeline enables:")
        print("  🎯 Intelligent talent matching")
        print("  🔍 Semantic resume search")
        print("  📊 Skills gap analysis")
        print("  🤖 AI-powered HR assistance")
    else:
        print("❌ Pipeline demo failed at CV extraction step")


if __name__ == "__main__":
    main()
