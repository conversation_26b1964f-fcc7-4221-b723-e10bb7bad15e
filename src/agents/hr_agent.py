from typing import TypedDict, Annotated

from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.messages import SystemMessage
from langchain_core.tools import tool
from langchain_deepseek import ChatDeepSeek
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START
from langgraph.graph import StateGraph, add_messages
from langgraph.prebuilt import tools_condition, ToolNode
from langgraph.types import interrupt

from agents.prompt_lib import EXTRACTOR_AGENT_INSTRUCTIONS

class State(TypedDict):
    """State for HR agent."""
    """Updates to messages will be appended to the existing list rather than overwriting it."""
    messages: Annotated[list, add_messages]

@tool
def human_assistance(query: str) -> str:
    """Request assistance from a human."""
    human_response = interrupt({"query": query})
    return human_response["data"]

tool_search = DuckDuckGoSearchRun()
tools = [tool_search]

llm = ChatDeepSeek(model="deepseek-chat")
llm_with_tools = llm.bind_tools(tools)


def chatbot(state: State) -> State:
    """A simple chatbot function that returns the current state."""
    system_message = SystemMessage(content=EXTRACTOR_AGENT_INSTRUCTIONS)
    messages = [system_message] + state["messages"]
    response = llm_with_tools.invoke(messages)
    assert (len(response.tool_calls) <= 1)
    return {"messages": [response]}


def stream_graph_updates(user_message: str):
    config = {"configurable": {"thread_id": "1"}}
    events = hr_agent.stream(
        {"messages": [{"role": "user", "content": user_message}]},
        config,
        stream_mode="values",
    )
    for event in events:
        event["messages"][-1].pretty_print()


graph_builder = StateGraph(State)

tool_node = ToolNode(tools=tools)
graph_builder.add_node("tools", tool_node)
graph_builder.add_node("chatbot", chatbot)

graph_builder.add_conditional_edges(
    "chatbot",
    tools_condition,
)
graph_builder.add_edge("tools", "chatbot")
graph_builder.add_edge(START, "chatbot")

## Build the graph
memory = MemorySaver()
hr_agent = graph_builder.compile(checkpointer=memory)

# while True:
#     try:
#         user_input = input("User: ")
#         if user_input.lower() in ["quit", "exit", "q"]:
#             print("Goodbye!")
#             break
#         stream_graph_updates(user_input)
#     except:
#         # fallback if input() is not available
#         user_input = "What do you know about LangGraph?"
#         print("User: " + user_input)
#         stream_graph_updates(user_input)
#         break
