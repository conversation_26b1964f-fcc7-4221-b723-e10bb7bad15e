version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**********************************/codeplus_platform
      - NODE_ENV=development
      - PORT=8080
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./:/app
      - /app/node_modules

  db:
    image: postgres:16-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=codeplus_platform
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d codeplus_platform"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: