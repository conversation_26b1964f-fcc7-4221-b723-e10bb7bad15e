{"memories": [{"id": "coding-guideline-langgraph-docs", "content": "Always find the related docs about agent implementation when editing code in Python files. The docs directory is located at docs/langgraph_docs_and_examples", "tags": ["coding", "guideline", "agent", "documentation", "python"], "timestamp": "2025-05-30T00:00:00Z", "importance": "high"}, {"id": "documentation-consolidation-guideline", "content": "All major documentation should be consolidated into the main DEVOPS_CICD_GUIDE.md file. Avoid creating separate documentation files for features that can be integrated into existing guides. This prevents documentation fragmentation and improves maintainability.", "tags": ["documentation", "guideline", "consolidation", "maintenance"], "timestamp": "2025-05-31T00:00:00Z", "importance": "high"}, {"id": "ai-tool-testing-structure", "content": "AI-generated tests should be organized in ai-tool-tests directories within each component (ai-agent, frontend, streamlit). Structure: ai-tool-tests/{copilot,claude,chatgpt,other}/ with README files explaining purpose and guidelines. Tests should be reviewed before integration into main test suite.", "tags": ["testing", "ai-tools", "organization", "structure"], "timestamp": "2025-05-31T00:00:00Z", "importance": "medium"}, {"id": "portainer-webhook-integration", "content": "Portainer webhook system is fully implemented with service-specific URLs for frontend, agent-service, and streamlit-app. Webhooks are integrated into CD pipeline and deployment scripts. Manual notifications available via scripts/notify-portainer.sh. Documentation consolidated in DEVOPS_CICD_GUIDE.md.", "tags": ["<PERSON><PERSON><PERSON>", "webhooks", "deployment", "automation", "ci-cd"], "timestamp": "2025-05-31T00:00:00Z", "importance": "high"}]}