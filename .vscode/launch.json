{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI: Debug Backend",
            "type": "debugpy",
            "request": "launch",
            "program": "src/run_service.py",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "console": "integratedTerminal",
            "justMyCode": true // to focus debugging on your code rather than library code
        },
        {
            "name": "Streamlit: Debug App",
            "type": "debugpy",
            "request": "launch",
            "module": "streamlit",
            "args": [
                "run",
                "src/streamlit_app.py"
            ],
            "console": "integratedTerminal"
        }
    ]
}