import { Request, Response } from 'express';
import { ServiceRegistry } from '../services';
import { CreateUserDto, AddSkillToUserDto } from '../services/interfaces';

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management endpoints
 */
export class UserController {
  constructor(private readonly serviceRegistry: ServiceRegistry) {}

  /**
   * @swagger
   * /api/users:
   *   get:
   *     summary: Get all users
   *     tags: [Users]
   *     responses:
   *       200:
   *         description: List of all users
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/User'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getUsers = async (_: Request, res: Response): Promise<void> => {
    try {
      const userService = this.serviceRegistry.getUserService();
      const users = await userService.getUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  };

  /**
   * @swagger
   * /api/users/{id}:
   *   get:
   *     summary: Get user by ID with skills
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: User ID
   *     responses:
   *       200:
   *         description: User details with skills
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UserWithSkills'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getUserById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userService = this.serviceRegistry.getUserService();
      const user = await userService.getUserById(Number(id));
      
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }
        res.json(user);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch user' });
    }
  };

  /**
   * @swagger
   * /api/users/{userId}/skills:
   *   get:
   *     summary: Get all skills for a specific user
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: The user ID
   *     responses:
   *       200:
   *         description: List of user skills
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 type: object
   *                 properties:
   *                   userId:
   *                     type: integer
   *                   skillId:
   *                     type: integer
   *                   skill:
   *                     $ref: '#/components/schemas/Skill'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getUserSkills = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const userService = this.serviceRegistry.getUserService();
      
      // First check if user exists
      const user = await userService.getUserById(Number(userId));
      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }
      
      const userSkills = await userService.getUserSkills(Number(userId));
      res.json(userSkills);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch user skills' });
    }
  };

  /**
   * @swagger
   * /api/users:
   *   post:
   *     summary: Create a new user
   *     tags: [Users]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateUserDto'
   *     responses:
   *       201:
   *         description: User created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/User'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public createUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, email }: CreateUserDto = req.body;
      
      if (!name || !email) {
        res.status(400).json({ error: 'Name and email are required' });
        return;
      }

      const userService = this.serviceRegistry.getUserService();
      const user = await userService.createUser({ name, email });
      res.status(201).json(user);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create user' });
    }  };

  /**
   * @swagger
   * /api/users/{id}:
   *   put:
   *     summary: Update a user
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: User ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               email:
   *                 type: string
   *                 format: email
   *     responses:
   *       200:
   *         description: User updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/User'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public updateUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const data: Partial<CreateUserDto> = req.body;
      
      const userService = this.serviceRegistry.getUserService();
      const user = await userService.updateUser(Number(id), data);
      res.json(user);
    } catch (error) {
      res.status(500).json({ error: 'Failed to update user' });
    }
  };

  /**
   * @swagger
   * /api/users/{id}:
   *   delete:
   *     summary: Delete a user
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: User ID
   *     responses:
   *       204:
   *         description: User deleted successfully
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userService = this.serviceRegistry.getUserService();
      await userService.deleteUser(Number(id));
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete user' });
    }
  };

  /**
   * @swagger
   * /api/users/{userId}/skills:
   *   post:
   *     summary: Add a skill to a user
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: User ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/AddSkillToUserDto'
   *     responses:
   *       201:
   *         description: Skill added to user successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: integer
   *                 userId:
   *                   type: integer
   *                 skillId:
   *                   type: integer
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public addSkillToUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = Number(req.params.userId);
      const { skillId }: AddSkillToUserDto = req.body;

      if (!skillId) {
        res.status(400).json({ error: 'skillId is required' });
        return;
      }

      const userService = this.serviceRegistry.getUserService();
      const userSkill = await userService.addSkillToUser(userId, { skillId });
      res.status(201).json(userSkill);
    } catch (error) {
      res.status(500).json({ error: 'Failed to add skill to user' });
    }
  };

  /**
   * @swagger
   * /api/users/{userId}/skills/{skillId}:
   *   delete:
   *     summary: Remove a skill from a user
   *     tags: [Users]
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: User ID
   *       - in: path
   *         name: skillId
   *         required: true
   *         schema:
   *           type: integer
   *         description: Skill ID
   *     responses:
   *       204:
   *         description: Skill removed from user successfully
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public removeSkillFromUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = Number(req.params.userId);
      const { skillId } = req.params;

      const userService = this.serviceRegistry.getUserService();
      await userService.removeSkillFromUser(userId, Number(skillId));
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Failed to remove skill from user' });
    }
  };
}