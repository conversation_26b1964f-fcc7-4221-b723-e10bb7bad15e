.title {
  color: var(--mantine-color-gray-9);
  font-weight: 700;
  margin-bottom: var(--mantine-spacing-xs);
}

.developmentAlert {
  border-left: 4px solid var(--mantine-color-blue-6);
}

.content {
  background-color: var(--mantine-color-gray-0);
  padding: var(--mantine-spacing-xl);
  border-radius: var(--mantine-radius-md);
  border: 1px solid var(--mantine-color-gray-3);
}

.featureList {
  margin: var(--mantine-spacing-md) 0;
  padding-left: var(--mantine-spacing-lg);
}

.featureList li {
  margin-bottom: var(--mantine-spacing-xs);
  color: var(--mantine-color-gray-7);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .title {
    color: var(--mantine-color-dark-0);
  }

  .content {
    background-color: var(--mantine-color-dark-7);
    border-color: var(--mantine-color-dark-5);
  }

  .featureList li {
    color: var(--mantine-color-dark-2);
  }
}
