// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  //output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int       @id @default(autoincrement())
  name      String
  email     String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  skills    UserSkill[]
}

model Skill {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  category    Category  @relation(fields: [categoryId], references: [id])
  categoryId  Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  users       UserSkill[]
}

model Category {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  skills      Skill[]
}

model UserSkill {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  skill     Skill    @relation(fields: [skillId], references: [id])
  skillId   Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, skillId])
}
