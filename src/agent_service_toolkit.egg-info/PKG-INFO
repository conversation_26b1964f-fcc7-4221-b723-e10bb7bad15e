Metadata-Version: 2.4
Name: agent-service-toolkit
Version: 0.1.0
Summary: Full toolkit for running an AI agent service built with LangGraph, FastAPI and Streamlit
Author-email: <PERSON> <<EMAIL>>
Classifier: Development Status :: 4 - Beta
Classifier: License :: OSI Approved :: MIT License
Classifier: Framework :: FastAPI
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.11
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: duckduckgo-search>=7.3.0
Requires-Dist: fastapi~=0.115.5
Requires-Dist: grpcio>=1.68.0
Requires-Dist: httpx~=0.27.2
Requires-Dist: jiter~=0.8.2
Requires-Dist: langchain-core~=0.3.33
Requires-Dist: langchain-community~=0.3.16
Requires-Dist: langchain-anthropic~=0.3.0
Requires-Dist: langchain-aws~=0.2.14
Requires-Dist: langchain-chroma~=0.2.3
Requires-Dist: langchain-google-genai~=2.0.11
Requires-Dist: langchain-google-vertexai>=2.0.7
Requires-Dist: langchain-groq~=0.2.1
Requires-Dist: langchain-ollama~=0.2.3
Requires-Dist: langchain-openai~=0.3.0
Requires-Dist: langgraph~=0.3.5
Requires-Dist: langgraph-checkpoint-mongodb~=0.1.3
Requires-Dist: langgraph-checkpoint-postgres~=2.0.13
Requires-Dist: langgraph-checkpoint-sqlite~=2.0.1
Requires-Dist: langgraph-supervisor~=0.0.8
Requires-Dist: langsmith~=0.1.145
Requires-Dist: numexpr~=2.10.1
Requires-Dist: numpy~=1.26.4; python_version <= "3.12"
Requires-Dist: numpy~=2.2.3; python_version >= "3.13"
Requires-Dist: onnxruntime~=1.21.1
Requires-Dist: pandas~=2.2.3
Requires-Dist: psycopg[binary,pool]~=3.2.4
Requires-Dist: pyarrow>=18.1.0
Requires-Dist: pydantic~=2.10.1
Requires-Dist: pydantic-settings~=2.6.1
Requires-Dist: pyowm~=3.3.0
Requires-Dist: python-dotenv~=1.0.1
Requires-Dist: setuptools~=75.6.0
Requires-Dist: streamlit~=1.40.1
Requires-Dist: tiktoken>=0.8.0
Requires-Dist: uvicorn~=0.32.1
Requires-Dist: pypdf~=5.3.0
Requires-Dist: docx2txt~=0.8
Dynamic: license-file


## Prerequisites

- Docker
- Docker Compose

## Running and Debugging the Application Locally

### 1. Install Python and Virtual Environment Tools

Make sure you have Python 3.1x or higher installed.  
Install `uv` (a fast Python package manager) and `virtualenv` if you don't have them:

```bash
pip install uv virtualenv

## Create and Activate a Virtual Environment
python3 -m venv .venv
source .venv/bin/activate

##  Install Python Dependencies
uv pip install -r [requirements.txt]
```

### 2. Run the Application

**Activate virtual environment**

```bash
source .venv/bin/activate
```

**FastAPI backend**
```bash
python src/run_service.py
```
**Streamlit app (UI)**
```bash
streamlit run src/streamlit_app.py
```
### 3. Debug with VS Code
    1. Open the project in VS Code.
    2. Go to the Run & Debug panel (left sidebar).
    3. Select either "FastAPI: Debug Backend" or "Streamlit: Debug App" from the dropdown.
    4. Press F5 or click the green play button to start debugging.
    5. Set breakpoints in your code as needed.

## How to run with docker

```bash
docker compose watch
```

- Open http://localhost:8501 (Streamlit app)
- Service: http://localhost:8000
- Postgres database: localhost:5432 / acc/pass: postgres/postgres

## Debug by Langgprah studio

The agent supports [LangGraph Studio](https://github.com/langchain-ai/langgraph-studio), a new IDE for developing agents in LangGraph.

You can simply install LangGraph Studio, add your `.env` file to the root directory as described above, and then launch LangGraph studio pointed at the root directory. Customize `langgraph.json` as needed.

## How to add new dependecies.
1. Add the dependency to `pyproject.toml`
2. Run the script script `script/generate_requirements.sh upgrade` to update `requirements.txt`

### Additional setup for specific AI providers

- [Setting up Ollama](docs/designs/guidelines/Ollama.md)
- [Setting up VertexAI](docs/designs/guidelines/VertexAI.md)
- [Setting up RAG with ChromaDB](docs/designs/guidelines/RAG_Assistant.md)

## Building or customizing your own agent

To customize the agent for your own use case:

1. Add your new agent to the `src/agents` directory. You can copy `research_assistant.py` or `chatbot.py` and modify it to change the agent's behavior and tools.
1. Import and add your new agent to the `agents` dictionary in `src/agents/agents.py`. Your agent can be called by `/<your_agent_name>/invoke` or `/<your_agent_name>/stream`.
1. Adjust the Streamlit interface in `src/streamlit_app.py` to match your agent's capabilities.

### Key Features

1. **LangGraph Agent and latest features**: A customizable agent built using the LangGraph framework. Implements the latest LangGraph v0.3 features including human in the loop with `interrupt()`, flow control with `Command`, long-term memory with `Store`, and `langgraph-supervisor`.
1. **FastAPI Service**: Serves the agent with both streaming and non-streaming endpoints.
1. **Advanced Streaming**: A novel approach to support both token-based and message-based streaming.
1. **Streamlit Interface**: Provides a user-friendly chat interface for interacting with the agent.
1. **Multiple Agent Support**: Run multiple agents in the service and call by URL path. Available agents and models are described in `/info`
1. **Asynchronous Design**: Utilizes async/await for efficient handling of concurrent requests.
1. **Content Moderation**: Implements LlamaGuard for content moderation (requires Groq API key).
1. **RAG Agent**: A basic RAG agent implementation using ChromaDB - see [docs](docs/RAG_Assistant.md).
1. **Feedback Mechanism**: Includes a star-based feedback system integrated with LangSmith.
1. **Docker Support**: Includes Dockerfiles and a docker compose file for easy development and deployment.
1. **Testing**: Includes robust unit and integration tests for the full repo.

### Key Files

The repository is structured as follows:

- `src/agents/`: Defines several agents with different capabilities
- `src/schema/`: Defines the protocol schema
- `src/core/`: Core modules including LLM definition and settings
- `src/service/service.py`: FastAPI service to serve the agents
- `src/client/client.py`: Client to interact with the agent service
- `src/streamlit_app.py`: Streamlit app providing a chat interface
- `tests/`: Unit and integration tests

## References

The following are a few of the public projects that drew code or inspiration from this repo.

- **[PolyRAG](https://github.com/QuentinFuxa/PolyRAG)** - Extends agent-service-toolkit with RAG capabilities over both PostgreSQL databases and PDF documents.
- **[alexrisch/agent-web-kit](https://github.com/alexrisch/agent-web-kit)** - A Next.JS frontend for agent-service-toolkit
- **[raushan-in/dapa](https://github.com/raushan-in/dapa)** - Digital Arrest Protection App (DAPA) enables users to report financial scams and frauds efficiently via a user-friendly platform.
