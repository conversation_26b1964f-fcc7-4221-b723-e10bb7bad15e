import math
import os
import re
from typing import Any, List

import numexpr

# Set environment variables to disable telemetry completely
os.environ.setdefault("ANONYMIZED_TELEMETRY", "false")
os.environ.setdefault("CHROMA_TELEMETRY_DISABLED", "true")
os.environ.setdefault("OTEL_SDK_DISABLED", "true")
os.environ.setdefault("OTEL_PYTHON_DISABLED", "true")
os.environ.setdefault("OTEL_TRACES_EXPORTER", "none")
os.environ.setdefault("OTEL_METRICS_EXPORTER", "none")
os.environ.setdefault("OTEL_LOGS_EXPORTER", "none")

# Comprehensive OpenTelemetry patching before any imports
import sys
import types

# Create mock modules for all OpenTelemetry components that might be imported
class MockBatchSpanProcessor:
    def __init__(self, *args, **kwargs):
        pass
    def shutdown(self, *args, **kwargs):
        return None
    def force_flush(self, *args, **kwargs):
        return None

class MockSpanExporter:
    def __init__(self, *args, **kwargs):
        pass
    def export(self, *args, **kwargs):
        return None
    def shutdown(self, *args, **kwargs):
        return None

otel_mock_modules = {
    'opentelemetry': {},
    'opentelemetry.context': {'_load_runtime_context': lambda: None},
    'opentelemetry.sdk': {},
    'opentelemetry.sdk.metrics': {},
    'opentelemetry.sdk.metrics.export': {'MetricsData': object},
    'opentelemetry.sdk.trace': {'TracerProvider': object},
    'opentelemetry.sdk.trace.export': {
        'SpanExporter': MockSpanExporter,
        'SpanExportResult': object,
        'BatchSpanProcessor': MockBatchSpanProcessor
    },
    'opentelemetry.exporter': {},
    'opentelemetry.exporter.otlp': {},
    'opentelemetry.exporter.otlp.proto': {},
    'opentelemetry.exporter.otlp.proto.grpc': {},
    'opentelemetry.exporter.otlp.proto.grpc.trace_exporter': {'OTLPSpanExporter': object},
    'opentelemetry.exporter.otlp.proto.grpc.exporter': {},
}

for module_name, attrs in otel_mock_modules.items():
    if module_name not in sys.modules:
        mock_module = types.ModuleType(module_name)
        for attr_name, attr_value in attrs.items():
            setattr(mock_module, attr_name, attr_value)
        sys.modules[module_name] = mock_module

# Now try to import ChromaDB
from langchain_chroma import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_core.tools import tool
from langchain_core.vectorstores import VectorStoreRetriever


@tool(description="Calculates a math expression using numexpr. Useful for when you need to answer questions about math using numexpr. This tool is only for math questions and nothing else. Only input math expressions.")
def calculator(expression: str) -> str:
    """Calculates a math expression using numexpr.

    Useful for when you need to answer questions about math using numexpr.
    This tool is only for math questions and nothing else. Only input
    math expressions.

    Args:
        expression (str): A valid numexpr formatted math expression.

    Returns:
        str: The result of the math expression.
    """

    try:
        local_dict = {"pi": math.pi, "e": math.e}
        output = str(
            numexpr.evaluate(
                expression.strip(),
                global_dict={},  # restrict access to globals
                local_dict=local_dict,  # add common mathematical functions
            )
        )
        return re.sub(r"^\[|\]$", "", output)
    except Exception as e:
        raise ValueError(
            f'calculator("{expression}") raised error: {e}.'
            " Please try again with a valid numerical expression"
        )

# calculator is now the tool created by the @tool decorator


# Format retrieved documents
def format_contexts(docs: List[Any]) -> str:
    return "\n\n".join(doc.page_content for doc in docs)


def load_chroma_db() -> VectorStoreRetriever:
    # Create the embedding function for our project description database
    try:
        # Use HuggingFace embeddings (local model)
        embeddings = HuggingFaceEmbeddings(
            model_name="all-MiniLM-L6-v2",  # Small, fast model that works well
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )
    except Exception as e:
        raise RuntimeError(
            "Failed to initialize HuggingFaceEmbeddings. Make sure the model is installed."
        ) from e

    # Load the stored vector database
    chroma_db = Chroma(persist_directory="./chroma_db", embedding_function=embeddings)
    retriever = chroma_db.as_retriever(search_kwargs={"k": 5})
    return retriever


@tool(description="Searches the company's knowledge base stored in ChromaDB for relevant information. This tool searches through the company's handbook and documentation stored in a ChromaDB vector database to find relevant information based on the query.")
def database_search(query: str) -> str:
    """Searches the company's knowledge base stored in ChromaDB for relevant information.

    This tool searches through the company's handbook and documentation stored in a
    ChromaDB vector database to find relevant information based on the query.

    Args:
        query (str): The search query to find relevant information in the knowledge base.

    Returns:
        str: Formatted text containing relevant documents found in the database.
    """
    # Get the chroma retriever
    retriever = load_chroma_db()

    # Search the database for relevant documents
    documents = retriever.invoke(query)

    # Format the documents into a string
    context_str = format_contexts(documents)

    return context_str


# database_search is now the tool created by the @tool decorator
